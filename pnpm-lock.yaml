lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    devDependencies:
      turbo:
        specifier: ^2.5.4
        version: 2.5.4

  packages/client:
    dependencies:
      '@fluentui/react-components':
        specifier: ^9.66.1
        version: 9.66.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@react-router/node':
        specifier: ^7.5.3
        version: 7.6.2(react-router@7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(typescript@5.8.3)
      '@react-router/serve':
        specifier: ^7.5.3
        version: 7.6.2(react-router@7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(typescript@5.8.3)
      isbot:
        specifier: ^5.1.27
        version: 5.1.28
      react:
        specifier: ^18.3.1
        version: 18.3.1
      react-dom:
        specifier: ^18.3.1
        version: 18.3.1(react@18.3.1)
      react-router:
        specifier: ^7.5.3
        version: 7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
    devDependencies:
      '@react-router/dev':
        specifier: ^7.5.3
        version: 7.6.2(@react-router/serve@7.6.2(react-router@7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(typescript@5.8.3))(@types/node@20.19.0)(jiti@2.4.2)(lightningcss@1.30.1)(react-router@7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(typescript@5.8.3)(vite@6.3.5(@types/node@20.19.0)(jiti@2.4.2)(lightningcss@1.30.1))
      '@types/node':
        specifier: ^20
        version: 20.19.0
      '@types/react':
        specifier: ^18.3.23
        version: 18.3.23
      '@types/react-dom':
        specifier: ^18.3.7
        version: 18.3.7(@types/react@18.3.23)
      typescript:
        specifier: ^5.8.3
        version: 5.8.3
      vite:
        specifier: ^6.3.3
        version: 6.3.5(@types/node@20.19.0)(jiti@2.4.2)(lightningcss@1.30.1)
      vite-tsconfig-paths:
        specifier: ^5.1.4
        version: 5.1.4(typescript@5.8.3)(vite@6.3.5(@types/node@20.19.0)(jiti@2.4.2)(lightningcss@1.30.1))

  packages/client-19:
    dependencies:
      '@react-router/node':
        specifier: ^7.5.3
        version: 7.6.2(react-router@7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(typescript@5.8.3)
      '@react-router/serve':
        specifier: ^7.5.3
        version: 7.6.2(react-router@7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(typescript@5.8.3)
      isbot:
        specifier: ^5.1.27
        version: 5.1.28
      react:
        specifier: ^18.3.1
        version: 18.3.1
      react-dom:
        specifier: ^18.3.1
        version: 18.3.1(react@18.3.1)
      react-router:
        specifier: ^7.5.3
        version: 7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
    devDependencies:
      '@react-router/dev':
        specifier: ^7.5.3
        version: 7.6.2(@react-router/serve@7.6.2(react-router@7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(typescript@5.8.3))(@types/node@20.19.0)(jiti@2.4.2)(lightningcss@1.30.1)(react-router@7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(typescript@5.8.3)(vite@6.3.5(@types/node@20.19.0)(jiti@2.4.2)(lightningcss@1.30.1))
      '@tailwindcss/vite':
        specifier: ^4.1.4
        version: 4.1.10(vite@6.3.5(@types/node@20.19.0)(jiti@2.4.2)(lightningcss@1.30.1))
      '@types/node':
        specifier: ^20
        version: 20.19.0
      '@types/react':
        specifier: ^19.1.2
        version: 19.1.8
      '@types/react-dom':
        specifier: ^19.1.2
        version: 19.1.6(@types/react@19.1.8)
      tailwindcss:
        specifier: ^4.1.4
        version: 4.1.10
      typescript:
        specifier: ^5.8.3
        version: 5.8.3
      vite:
        specifier: ^6.3.3
        version: 6.3.5(@types/node@20.19.0)(jiti@2.4.2)(lightningcss@1.30.1)
      vite-tsconfig-paths:
        specifier: ^5.1.4
        version: 5.1.4(typescript@5.8.3)(vite@6.3.5(@types/node@20.19.0)(jiti@2.4.2)(lightningcss@1.30.1))

packages:

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==}
    engines: {node: '>=6.0.0'}

  '@babel/code-frame@7.27.1':
    resolution: {integrity: sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.27.5':
    resolution: {integrity: sha512-KiRAp/VoJaWkkte84TvUd9qjdbZAdiqyvMxrGl1N6vzFogKmaLgoM3L1kgtLicp2HP5fBJS8JrZKLVIZGVJAVg==}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.27.4':
    resolution: {integrity: sha512-bXYxrXFubeYdvB0NhD/NBB3Qi6aZeV20GOWVI47t2dkecCEoneR4NPVcb7abpXDEvejgrUfFtG6vG/zxAKmg+g==}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.27.5':
    resolution: {integrity: sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-annotate-as-pure@7.27.3':
    resolution: {integrity: sha512-fXSwMQqitTGeHLBC08Eq5yXz2m37E4pJX1qAU1+2cNedz/ifv/bVXft90VeSav5nFO61EcNgwr0aJxbyPaWBPg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.27.2':
    resolution: {integrity: sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-create-class-features-plugin@7.27.1':
    resolution: {integrity: sha512-QwGAmuvM17btKU5VqXfb+Giw4JcN0hjuufz3DYnpeVDvZLAObloM77bhMXiqry3Iio+Ai4phVRDwl6WU10+r5A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-member-expression-to-functions@7.27.1':
    resolution: {integrity: sha512-E5chM8eWjTp/aNoVpcbfM7mLxu9XGLWYise2eBKGQomAk/Mb4XoxyqXTZbuTohbsl8EKqdlMhnDI2CCLfcs9wA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.27.1':
    resolution: {integrity: sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.27.3':
    resolution: {integrity: sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-optimise-call-expression@7.27.1':
    resolution: {integrity: sha512-URMGH08NzYFhubNSGJrpUEphGKQwMQYBySzat5cAByY1/YgIRkULnIy3tAMeszlL/so2HbeilYloUmSpd7GdVw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-plugin-utils@7.27.1':
    resolution: {integrity: sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-replace-supers@7.27.1':
    resolution: {integrity: sha512-7EHz6qDZc8RYS5ElPoShMheWvEgERonFCs7IAonWLLUTXW59DP14bCZt89/GKyreYn8g3S83m21FelHKbeDCKA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    resolution: {integrity: sha512-Tub4ZKEXqbPjXgWLl2+3JpQAYBJ8+ikpQ2Ocj/q/r0LwE3UhENh7EUabyHjz2kCEsrRY83ew2DQdHluuiDQFzg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.27.1':
    resolution: {integrity: sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.27.1':
    resolution: {integrity: sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.27.1':
    resolution: {integrity: sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.27.6':
    resolution: {integrity: sha512-muE8Tt8M22638HU31A3CgfSUciwz1fhATfoVai05aPXGor//CdWDCbnlY1yvBPo07njuVOCNGCSp/GTt12lIug==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.27.5':
    resolution: {integrity: sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-syntax-decorators@7.27.1':
    resolution: {integrity: sha512-YMq8Z87Lhl8EGkmb0MwYkt36QnxC+fzCgrl66ereamPlYToRpIk5nUjKUY3QKLWq8mwUB1BgbeXcTJhZOCDg5A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-jsx@7.27.1':
    resolution: {integrity: sha512-y8YTNIeKoyhGd9O0Jiyzyyqk8gdjnumGTQPsz0xOZOQ2RmkVJeZ1vmmfIvFEKqucBG6axJGBZDE/7iI5suUI/w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-typescript@7.27.1':
    resolution: {integrity: sha512-xfYCBMxveHrRMnAWl1ZlPXOZjzkN82THFvLhQhFXFt81Z5HnN+EtUkZhv/zcKpmT3fzmWZB0ywiBrbC3vogbwQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-commonjs@7.27.1':
    resolution: {integrity: sha512-OJguuwlTYlN0gBZFRPqwOGNWssZjfIUdS7HMYtN8c1KmwpwHFBwTeFZrg9XZa+DFTitWOW5iTAG7tyCUPsCCyw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typescript@7.27.1':
    resolution: {integrity: sha512-Q5sT5+O4QUebHdbwKedFBEwRLb02zJ7r4A5Gg2hUoLuU3FjdMcyqcywqUrLCaDsFCxzokf7u9kuy7qz51YUuAg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/preset-typescript@7.27.1':
    resolution: {integrity: sha512-l7WfQfX0WK4M0v2RudjuQK4u99BS6yLHYEmdtVPP7lKV013zr9DygFuWNlnbvQ9LR+LS0Egz/XAvGx5U9MX0fQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/runtime@7.27.6':
    resolution: {integrity: sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q==}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.27.2':
    resolution: {integrity: sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.27.4':
    resolution: {integrity: sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.27.6':
    resolution: {integrity: sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==}
    engines: {node: '>=6.9.0'}

  '@ctrl/tinycolor@3.6.1':
    resolution: {integrity: sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA==}
    engines: {node: '>=10'}

  '@emotion/hash@0.9.2':
    resolution: {integrity: sha512-MyqliTZGuOm3+5ZRSaaBGP3USLw6+EGykkwZns2EPC5g8jJ4z9OrdZY9apkl3+UP9+sdz76YYkwCKP5gh8iY3g==}

  '@esbuild/aix-ppc64@0.25.5':
    resolution: {integrity: sha512-9o3TMmpmftaCMepOdA5k/yDw8SfInyzWWTjYTFCX3kPSDJMROQTb8jg+h9Cnwnmm1vOzvxN7gIfB5V2ewpjtGA==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/android-arm64@0.25.5':
    resolution: {integrity: sha512-VGzGhj4lJO+TVGV1v8ntCZWJktV7SGCs3Pn1GRWI1SBFtRALoomm8k5E9Pmwg3HOAal2VDc2F9+PM/rEY6oIDg==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.25.5':
    resolution: {integrity: sha512-AdJKSPeEHgi7/ZhuIPtcQKr5RQdo6OO2IL87JkianiMYMPbCtot9fxPbrMiBADOWWm3T2si9stAiVsGbTQFkbA==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.25.5':
    resolution: {integrity: sha512-D2GyJT1kjvO//drbRT3Hib9XPwQeWd9vZoBJn+bu/lVsOZ13cqNdDeqIF/xQ5/VmWvMduP6AmXvylO/PIc2isw==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.25.5':
    resolution: {integrity: sha512-GtaBgammVvdF7aPIgH2jxMDdivezgFu6iKpmT+48+F8Hhg5J/sfnDieg0aeG/jfSvkYQU2/pceFPDKlqZzwnfQ==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.25.5':
    resolution: {integrity: sha512-1iT4FVL0dJ76/q1wd7XDsXrSW+oLoquptvh4CLR4kITDtqi2e/xwXwdCVH8hVHU43wgJdsq7Gxuzcs6Iq/7bxQ==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.25.5':
    resolution: {integrity: sha512-nk4tGP3JThz4La38Uy/gzyXtpkPW8zSAmoUhK9xKKXdBCzKODMc2adkB2+8om9BDYugz+uGV7sLmpTYzvmz6Sw==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.25.5':
    resolution: {integrity: sha512-PrikaNjiXdR2laW6OIjlbeuCPrPaAl0IwPIaRv+SMV8CiM8i2LqVUHFC1+8eORgWyY7yhQY+2U2fA55mBzReaw==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.25.5':
    resolution: {integrity: sha512-Z9kfb1v6ZlGbWj8EJk9T6czVEjjq2ntSYLY2cw6pAZl4oKtfgQuS4HOq41M/BcoLPzrUbNd+R4BXFyH//nHxVg==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.25.5':
    resolution: {integrity: sha512-cPzojwW2okgh7ZlRpcBEtsX7WBuqbLrNXqLU89GxWbNt6uIg78ET82qifUy3W6OVww6ZWobWub5oqZOVtwolfw==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.25.5':
    resolution: {integrity: sha512-sQ7l00M8bSv36GLV95BVAdhJ2QsIbCuCjh/uYrWiMQSUuV+LpXwIqhgJDcvMTj+VsQmqAHL2yYaasENvJ7CDKA==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.25.5':
    resolution: {integrity: sha512-0ur7ae16hDUC4OL5iEnDb0tZHDxYmuQyhKhsPBV8f99f6Z9KQM02g33f93rNH5A30agMS46u2HP6qTdEt6Q1kg==}
    engines: {node: '>=18'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.25.5':
    resolution: {integrity: sha512-kB/66P1OsHO5zLz0i6X0RxlQ+3cu0mkxS3TKFvkb5lin6uwZ/ttOkP3Z8lfR9mJOBk14ZwZ9182SIIWFGNmqmg==}
    engines: {node: '>=18'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.25.5':
    resolution: {integrity: sha512-UZCmJ7r9X2fe2D6jBmkLBMQetXPXIsZjQJCjgwpVDz+YMcS6oFR27alkgGv3Oqkv07bxdvw7fyB71/olceJhkQ==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.25.5':
    resolution: {integrity: sha512-kTxwu4mLyeOlsVIFPfQo+fQJAV9mh24xL+y+Bm6ej067sYANjyEw1dNHmvoqxJUCMnkBdKpvOn0Ahql6+4VyeA==}
    engines: {node: '>=18'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.25.5':
    resolution: {integrity: sha512-K2dSKTKfmdh78uJ3NcWFiqyRrimfdinS5ErLSn3vluHNeHVnBAFWC8a4X5N+7FgVE1EjXS1QDZbpqZBjfrqMTQ==}
    engines: {node: '>=18'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.25.5':
    resolution: {integrity: sha512-uhj8N2obKTE6pSZ+aMUbqq+1nXxNjZIIjCjGLfsWvVpy7gKCOL6rsY1MhRh9zLtUtAI7vpgLMK6DxjO8Qm9lJw==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-arm64@0.25.5':
    resolution: {integrity: sha512-pwHtMP9viAy1oHPvgxtOv+OkduK5ugofNTVDilIzBLpoWAM16r7b/mxBvfpuQDpRQFMfuVr5aLcn4yveGvBZvw==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [netbsd]

  '@esbuild/netbsd-x64@0.25.5':
    resolution: {integrity: sha512-WOb5fKrvVTRMfWFNCroYWWklbnXH0Q5rZppjq0vQIdlsQKuw6mdSihwSo4RV/YdQ5UCKKvBy7/0ZZYLBZKIbwQ==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-arm64@0.25.5':
    resolution: {integrity: sha512-7A208+uQKgTxHd0G0uqZO8UjK2R0DDb4fDmERtARjSHWxqMTye4Erz4zZafx7Di9Cv+lNHYuncAkiGFySoD+Mw==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openbsd]

  '@esbuild/openbsd-x64@0.25.5':
    resolution: {integrity: sha512-G4hE405ErTWraiZ8UiSoesH8DaCsMm0Cay4fsFWOOUcz8b8rC6uCvnagr+gnioEjWn0wC+o1/TAHt+It+MpIMg==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/sunos-x64@0.25.5':
    resolution: {integrity: sha512-l+azKShMy7FxzY0Rj4RCt5VD/q8mG/e+mDivgspo+yL8zW7qEwctQ6YqKX34DTEleFAvCIUviCFX1SDZRSyMQA==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.25.5':
    resolution: {integrity: sha512-O2S7SNZzdcFG7eFKgvwUEZ2VG9D/sn/eIiz8XRZ1Q/DO5a3s76Xv0mdBzVM5j5R639lXQmPmSo0iRpHqUUrsxw==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.25.5':
    resolution: {integrity: sha512-onOJ02pqs9h1iMJ1PQphR+VZv8qBMQ77Klcsqv9CNW2w6yLqoURLcgERAIurY6QE63bbLuqgP9ATqajFLK5AMQ==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.25.5':
    resolution: {integrity: sha512-TXv6YnJ8ZMVdX+SXWVBo/0p8LTcrUYngpWjvm91TMjjBQii7Oz11Lw5lbDV5Y0TzuhSJHwiH4hEtC1I42mMS0g==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [win32]

  '@floating-ui/core@1.7.1':
    resolution: {integrity: sha512-azI0DrjMMfIug/ExbBaeDVJXcY0a7EPvPjb2xAJPa4HeimBX+Z18HK8QQR3jb6356SnDDdxx+hinMLcJEDdOjw==}

  '@floating-ui/devtools@0.2.1':
    resolution: {integrity: sha512-8PHJLbD6VhBh+LJ1uty/Bz30qs02NXCE5u8WpOhSewlYXUWl03GNXknr9AS2yaAWJEQaY27x7eByJs44gODBcw==}
    peerDependencies:
      '@floating-ui/dom': '>=1.5.4'

  '@floating-ui/dom@1.7.1':
    resolution: {integrity: sha512-cwsmW/zyw5ltYTUeeYJ60CnQuPqmGwuGVhG9w0PRaRKkAyi38BT5CKrpIbb+jtahSwUl04cWzSx9ZOIxeS6RsQ==}

  '@floating-ui/utils@0.2.9':
    resolution: {integrity: sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg==}

  '@fluentui/keyboard-keys@9.0.8':
    resolution: {integrity: sha512-iUSJUUHAyTosnXK8O2Ilbfxma+ZyZPMua5vB028Ys96z80v+LFwntoehlFsdH3rMuPsA8GaC1RE7LMezwPBPdw==}

  '@fluentui/priority-overflow@9.1.15':
    resolution: {integrity: sha512-/3jPBBq64hRdA416grVj+ZeMBUIaKZk2S5HiRg7CKCAV1JuyF84Do0rQI6ns8Vb9XOGuc4kurMcL/UEftoEVrg==}

  '@fluentui/react-accordion@9.7.0':
    resolution: {integrity: sha512-DzWK3RBWlREn9EUYEXdYZhC6cjJLAm2u21qqofovrIlU/LDUUCC1cPxJHycdi9KwP7mDZdhXSqQG6LLe9xIeMQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-alert@9.0.0-beta.124':
    resolution: {integrity: sha512-yFBo3B5H9hnoaXxlkuz8wRz04DEyQ+ElYA/p5p+Vojf19Zuta8DmFZZ6JtWdtxcdnnQ4LvAfC5OYYlzdReozPA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-aria@9.15.0':
    resolution: {integrity: sha512-8cN9/5+XHL3mzp1gNIj0ZXuPTioYALO/1FCWugkOF5JP8PVkV3HDX3ezRq2Bk44PS2YK98tjffTiBzFeanHxug==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-avatar@9.8.1':
    resolution: {integrity: sha512-hLOFxN8oqRkO8lBqGhXLONtI4LRWf/16TJDiizWbfep33NMS/rpHl+PijwO873CXRxSDnR1z3sENHpVInILtug==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-badge@9.3.0':
    resolution: {integrity: sha512-BFONtrI0SZmM+j+wR8tb5S43qodY5AydKMCJ35e02rR1/nyizg4tA3g/3iujGHAAsXPX04D20W4QMcy9LyRAXA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-breadcrumb@9.2.1':
    resolution: {integrity: sha512-xwrwLz8AbvfcbESviNOrQD4GZ8YeabDK/WLzVXPf+sWsnPnnYx+j/+EgnsbTjJ8FtYKkak1pMq6KwLC1mzWQnQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-button@9.5.0':
    resolution: {integrity: sha512-J4Tdxcey6cjyxKuRAQkUynAwBwLnuTmGry9APGddbnGPGXBDNqjHIqqMDua5lOSIINSIiQHTNdg7fZWoETSZ4Q==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-card@9.3.0':
    resolution: {integrity: sha512-ZvCuFta3X2HaLTU0fdpbHCz/j+jGYRhwC0CVcxK1u4cXb74r4V2DfXaNYI9vXw9ELGe3YoiOE7xmDdDL0sRUYQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-carousel@9.7.1':
    resolution: {integrity: sha512-nmr1QCzH5vZHZ6KQ50YK+1obfKr/hejgqSMu1Ze/CwZ2/louEYzN2bhibtJfW6b3PpBeowL+S26jbdNWtI78yg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-checkbox@9.4.1':
    resolution: {integrity: sha512-lrf4I12fGMrodQODjrwTgDl5bOssXuEzg+ioMh/ldWQGD6xPjoqrznLusfjj+Ua1qR6k2bHnHuSDoH7E1vzlng==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-color-picker@9.1.0':
    resolution: {integrity: sha512-Tm85dMk0XPUZDCybjd0sa+1txR38ejLL+MG/Z03cpC41GxihDh5+4dPAqSfPzfezbENNoFsqfjKiKhw0Un96Rg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-combobox@9.15.1':
    resolution: {integrity: sha512-/WmfxkrYwe3/XU4gan56tjEBVdBmG43tW247vqXHQiC/e3q/dsqwQNhCO/VVr2pTS/Y3xhorMML63Azh9WXJ4A==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-components@9.66.1':
    resolution: {integrity: sha512-Rzh+QL2reQEMaFLu+h314ic7w8W9TbDcyDpohb+CRODgT3YCw+Gt+SVbR3Yi+8Cf3kwtokDQIC3ki6iBQ9g/Tg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-context-selector@9.2.0':
    resolution: {integrity: sha512-s35dNhIcHGm6SmmQr04vATaogQZ2Wvl1zi4/xgZ4/6V8XAGPBqRRTkutjkWgW4u4WZDriWdWNL62ju3hGDpE9g==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'
      scheduler: '>=0.19.0 <=0.23.0'

  '@fluentui/react-dialog@9.13.1':
    resolution: {integrity: sha512-YCGTh4IPaHQH1LTLoD5D5Ql7DK+1ytMHYL4kQ9O8CmSu3WntjUSmOKGxWDHqHLEX0gRz86fPy49/u5NDDhLfFA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-divider@9.3.0':
    resolution: {integrity: sha512-8MvWlNcYQBIpIH8d90PRLYvqTA53t0Folv1xf2isC+YWeTm5J1siZtPRiZ9+K0uqI9Y+RD4fnWN8HfMeyOAjlw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-drawer@9.8.1':
    resolution: {integrity: sha512-VjzG0qAXN7eXiBbFzM7YHpNes05YIdY3WHJD6V2FheHvmthzhw8GFqDnRHsZ581Wb9uB9xqi+WJ69vNJ9tS48Q==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-field@9.3.1':
    resolution: {integrity: sha512-9bzicAbR5+AtboowO6akbJsoMWDGUtbGenQT81mXt7HGg6RP86gpodgcr/4f1OG1w5VtrfoA/aoNExP/XzUeGg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-icons@2.0.303':
    resolution: {integrity: sha512-XsPCS6ZWpXytoYSiRAZY1De82JXdkEhmn82cNWsnxlixQATCOv5+JTW1Isogp84eISYVZE3KkXPwqye8fpKnjg==}
    peerDependencies:
      react: '>=16.8.0 <19.0.0'

  '@fluentui/react-image@9.2.0':
    resolution: {integrity: sha512-vP26rQDNx5LevbEKbf6dLjTx4uOZWIopjx6HQYSLk8axGWmjXe21t6BXRa9iTiPfibwJmWwzXvqGHxYR/as/wA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-infobutton@9.0.0-beta.102':
    resolution: {integrity: sha512-3kA4F0Vga8Ds6JGlBajLCCDOo/LmPuS786Wg7ui4ZTDYVIMzy1yp2XuVcZniifBFvEp0HQCUoDPWUV0VI3FfzQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-infolabel@9.3.1':
    resolution: {integrity: sha512-fL2J3PJy6ylPQrFFwAJgFoACxRk5d/PtzjL7JlmM1OCaUmUD2FuUovDYpubw9r36OemVOHTB/oXhpitlS3BoPQ==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-input@9.6.1':
    resolution: {integrity: sha512-IMwJxKjZYznlKFrZ6MoKpFXJxfGoJBJux4hDZzqDWyDafDSvjmTpiiutJbQmMRQpxQ4pPuaHBwcSScfExAf69g==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-jsx-runtime@9.1.0':
    resolution: {integrity: sha512-HB4+1ofzmweSWrFPZeoeepzNNHu54jplCfPLlppBoHx1MZ11RR9w2uIsLjfSDrEPIZnXbQxVBItvDh9ZrU9new==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'

  '@fluentui/react-label@9.2.0':
    resolution: {integrity: sha512-WDaBR9AmdPvJ0vXN9WicOlHFhI6BKgQXULl0YjMXuL51tl37txyvY2crv+YNeVsfykI18h6LOPxltPeEdAsxag==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-link@9.5.0':
    resolution: {integrity: sha512-bdEFARlbnTyzrKHKv7wvLMRua7/gUX1dOzBG+1tfmJFuFkE2gz7rxABBVdlaI1PHsgAbGnzQnSzl6C5DOPgqcA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-list@9.2.1':
    resolution: {integrity: sha512-UGRD+oBNtSRA+GH7n3qC07AatNvRLBQwSCoaza9ElYWsh4eWQzbp/zkurLWIM0PrAUd4JHuMswHARRBlJeY5gg==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-menu@9.17.1':
    resolution: {integrity: sha512-aygFQRa6Zt8sZ6aBnR+OiNaFOmykg+X5BTPBiu2m6IlJs1Z42S2AuSj8OuBUjrFQ3LnxT579AHDZuTXBngCsEQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-message-bar@9.5.0':
    resolution: {integrity: sha512-rsJUrXQWazdQ8gUX+l4XzToA8BMOJ+8t6WjXYr48Ztp7E9oROKaralavF78yihwY3t1ceacSbKa4bQLNqONlDw==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-motion-components-preview@0.6.0':
    resolution: {integrity: sha512-9PBaI25VGIuVKYE8Q4gew4/tsFmsOD4F1ZzHdEVkUS984pCZjC3LD5+6wrxpoJajDGk4cpWRRGl8x3DcO5CgHQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-motion@9.8.0':
    resolution: {integrity: sha512-TTwJV4iw7LHesPNtQpPmEb77YplC89Vh2+ru2vWS+f5YJbmduN4V/WH/ViakHjRGj/m03jRaQruTpg3rKGUCZw==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-nav@9.1.1':
    resolution: {integrity: sha512-kn+5KVDCoY/xPrpEegJv9SEVofqLOPLDWk2C5YBR0zZItzZ7cHfNxABsZ3fD0RM15ro5BaaHm6mfuDxERHluHA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-overflow@9.4.1':
    resolution: {integrity: sha512-qToEgEuyBWN2Te+9gg56fib/jCDwi3gBJhvZQSL8Ywgg3nNhmyAnOfGEdaMHrVL4DpFaNEOzxoC2C9vrzCx5bQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-persona@9.4.1':
    resolution: {integrity: sha512-+1LLEfSEsZqcYLKt80BPT7hPXwbP49SiOb5PSHvOM58HtruWtD+rx7xLFVcR9BnlJK/oZkRjisfQlAM3zuZ3Yw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-popover@9.11.1':
    resolution: {integrity: sha512-f+/K+8zHAlrUR16NSEtZ4rYArPtm+PpEuC9qd7+PjrlI/GytZHqVlNA8X4ddHWQy+fJoqTSA6cbB+SEYK8/yPw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-portal@9.6.0':
    resolution: {integrity: sha512-FiA3eM/1Um/3HZvfaGisdL7pLV4idWzlmDUIFBUOlzXsy57mIY9IwV5nDHYiJdEMkW0UstRVJB4oRaHoHGSqUg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-positioning@9.18.1':
    resolution: {integrity: sha512-+ueJus7IaezMAEDrlo3G/ihd+8Voa1W4dWrswH7Jknulggp8Mfaz1wMdZq8GvMuBnifMLJ33M9svsrJJahscPw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-progress@9.3.1':
    resolution: {integrity: sha512-2+jMPtuANnU7mUVEyUhhLh2LJmZNHrH4sin5rjSlsipr3ifhCoFUOoOloHw+cuVFzHeQNxIV9AuzOODii6cU3g==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-provider@9.21.0':
    resolution: {integrity: sha512-mADFjeZKN5e6AJJ45Nc99yDMmvzDPZea7G0PznByC4H/+JuZO3oExTve2SYSmj4KECyjv3wQVlMe7os9sCLZ6w==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-radio@9.4.1':
    resolution: {integrity: sha512-uQ+BeJeESBpC+MOC1coeiUlLVshpz2fjme3SKPuGDZv1x919Mh2e8OG5R1EcNGLJBMSVrU/LT8sqAV9WJ4k2cQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-rating@9.2.0':
    resolution: {integrity: sha512-GjEE6XmxDc8zTiQWZmiRJgXqKzreREQRUOimuBrG4exxKcoXj11Ah+oOrLJ/z/KmPyu0JGk5yHJ+VMuJeJh6gw==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-search@9.2.1':
    resolution: {integrity: sha512-tFfo72YnBLK4nIIpaL8IE0Qu1hHGOjbbl2TxM6NN9qddp0s+5WeUHtpE1auyMeY4s1UQNbZbtjmsBpzicCAlaQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-select@9.3.1':
    resolution: {integrity: sha512-BvylsBcUzH8t/miTo/kesuv6GgTW6AiipFkTFsoeKqXS4kWYOZx3+ufVytdU9Pcowr0WrSBy6s/206JCQR3nVg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-shared-contexts@9.23.1':
    resolution: {integrity: sha512-mP+7talxLz7n0G36o7Asdvst+JPzUbqbnoMKUWRVB5YwzlOXumEgaQDgL1BkRUJYaDGOjIiSTUjHOEkBt7iSdg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'

  '@fluentui/react-skeleton@9.3.1':
    resolution: {integrity: sha512-gI05SgPkrACHH7dy2ZM5had1/Px99Wpvsxl+gzBCzloqeNlm0Eh1H/TH5UdFOm+0IA/Lit/8crwqSNRmHL/Viw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-slider@9.4.1':
    resolution: {integrity: sha512-pJeh2gRXV4/uDbT2HAcWmp7zxq3Bwr48/LHzsPngwKP6W8Pgw7NysMZimJVs3B5nL4KXZyyH/ArDy6IV7pl/Aw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-spinbutton@9.4.1':
    resolution: {integrity: sha512-dUj4XEocE5Uy0TWFxFNVGyRZpNJCHNl/VNWwJcDPNf6Jb5ThqGcXZ4IgWO00GoucwTkUzIHE37SSBGatL3ANsA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-spinner@9.6.0':
    resolution: {integrity: sha512-yRUozOphh92DMM/hZLp2aF12vWGpz70M7ya//E0PVhwXMD2zJf7EvK/HvgdtMNoiSkM9nYrEoe4HuEialn2WQQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-swatch-picker@9.3.1':
    resolution: {integrity: sha512-W7Dz9pF39KdNdYLFR6ySa13et/i+5LLkY6HrGg9k3LxtAYwCeooy++4FBYpWE87i+FcuiAGKmzhy6vHM5i2TBA==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-switch@9.3.1':
    resolution: {integrity: sha512-QxmTGQQdUWpfGe40RafooeHeM8evAz6dItDsEEenu4h8KbrD0fztBjDG51fjuAPsrbYzoPS1o684+dD8pl2tNw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-table@9.17.1':
    resolution: {integrity: sha512-iDaX/wK4UmxYoqUPNK84553UTiYBB3YwPPjIkpxoxlv+RnjnPDshmDRT4KzCDNI2NvuhinwaKtj+b8DvMnFwHA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-tabs@9.8.0':
    resolution: {integrity: sha512-0dwF8v2rSRd7c3XV+LiHlf4eetXf79S2iBmLUZKmi+BQHWZv9NhmDLOw6DE8yidcHvlKlvXcUz+UNmVLXdmsCw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-tabster@9.25.0':
    resolution: {integrity: sha512-V0f0lWt/PZZ0ZDTz47qdvf4vQ5v0W2EZwhZlE2DTSiQ2U5hLAZhXKwCoM6T0nN+mviplQshNWBenbI6HS1RKgg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-tag-picker@9.6.1':
    resolution: {integrity: sha512-eQJHWpc8IfA/D/tsJZ2LOrPsm3CykRrRwIOl4qmRpxGF7jpjc9TTgv/x65xhNAV1zlHkn/kdeF3c6fg51ZPZYQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-tags@9.6.1':
    resolution: {integrity: sha512-h511CaowCakh1jXWFk7J2iy/7iXie0EafJqSYkES0fD/3whJOdos355veYkUqdD8G7BaMjL5n9Bkj3OjlxrMJw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-teaching-popover@9.5.1':
    resolution: {integrity: sha512-4YUcfbu/y2uY/gJGwo8EwcqegGBaFc6Mt4pKHLgUJd3m+26YDuHFEwpWEN/gHZ1nKsAXg/zlPpaPuDOwzFZFtQ==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-text@9.5.0':
    resolution: {integrity: sha512-mT//jeZDafU2zEBkSsRjLWtwJ6jyj/f5DPRZQ7/sA9yeQ4YDoXoJ2+x5IoG4VX4tkK1CRvmR4LA/V8JvrWjVyg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-textarea@9.5.1':
    resolution: {integrity: sha512-wGl2rHdv1ZONOSyIjjjbTI/SDRKV89rWF6yVS2qcCI5TFC5SoxadqG+u/9Fuy3kpv69WwRU8Op3mDSz+GYFa/A==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-theme@9.1.24':
    resolution: {integrity: sha512-OhVKYD7CMYHxzJEn4PtIszledj8hbQJNWBMfIZsp4Sytdp9vCi0txIQUx4BhS1WqtQPhNGCF16eW9Q3NRrnIrQ==}

  '@fluentui/react-toast@9.5.0':
    resolution: {integrity: sha512-TPgNNxfP5X80Pl/H7jVgreGKfDdEkN/L6G1rnjM18emsIw0DYB+B46JoBwmrPCvISZJNnLstSftwwDSqQEO2hg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-toolbar@9.5.1':
    resolution: {integrity: sha512-8lI8lrRMdm3q9K31iKrOXbC+65OnSi+GtO06FjcKd413x0fBAYbWweRciAh3IyIAiU38RdjIvLKiIs92TuqUpg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-tooltip@9.7.1':
    resolution: {integrity: sha512-LiIQDOGEsGeuAbiQItOL/OvSiX9gY5wKgUCduv1cSqQ2J/f3FbsPudBlQJs8UhukdT1jTqF7sjoNel6rMg/rNQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-tree@9.11.1':
    resolution: {integrity: sha512-ORRyUoDZzo0GOmiZKwnFlompCjVDi++5tBzf0o/8YQ0xOIlyuCp12oK0UI0AKATXC3lldTupmk0XSorbI4z4qg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-utilities@9.21.0':
    resolution: {integrity: sha512-xViS1WwKIdPza+syMsfh1i3hNgssWgLtbevEeGb6DS/q13UKXaw9P/vezPUs6kSolnSD/juWZGP6u8ytkI1W7g==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'

  '@fluentui/react-virtualizer@9.0.0-alpha.98':
    resolution: {integrity: sha512-BXLXsQPOS+IXrOoH0ZFBbEH6HI7zwGjWoiCPCkqexQYa54flDI8jo2xU7FrvYKVLVNK5oa+UA9jxw5GqDah8QQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/tokens@1.0.0-alpha.21':
    resolution: {integrity: sha512-xQ1T56sNgDFGl+kJdIwhz67mHng8vcwO7Dvx5Uja4t+NRULQBgMcJ4reUo4FGF3TjufHj08pP0/OnKQgnOaSVg==}

  '@griffel/core@1.19.2':
    resolution: {integrity: sha512-WkB/QQkjy9dE4vrNYGhQvRRUHFkYVOuaznVOMNTDT4pS9aTJ9XPrMTXXlkpcwaf0D3vNKoerj4zAwnU2lBzbOg==}

  '@griffel/react@1.5.30':
    resolution: {integrity: sha512-1q4ojbEVFY5YA0j1NamP0WWF4BKh+GHsVugltDYeEgEaVbH3odJ7tJabuhQgY+7Nhka0pyEFWSiHJev0K3FSew==}
    peerDependencies:
      react: '>=16.8.0 <20.0.0'

  '@griffel/style-types@1.3.0':
    resolution: {integrity: sha512-bHwD3sUE84Xwv4dH011gOKe1jul77M1S6ZFN9Tnq8pvZ48UMdY//vtES6fv7GRS5wXYT4iqxQPBluAiYAfkpmw==}

  '@isaacs/cliui@8.0.2':
    resolution: {integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==}
    engines: {node: '>=12'}

  '@isaacs/fs-minipass@4.0.1':
    resolution: {integrity: sha512-wgm9Ehl2jpeqP3zw/7mo3kRHFp5MEDhqAdwy1fTGkHAwnkGOVsgpvQhL8B5n1qlb01jV3n/bI0ZfZp5lWA1k4w==}
    engines: {node: '>=18.0.0'}

  '@jridgewell/gen-mapping@0.3.8':
    resolution: {integrity: sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.2.1':
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/sourcemap-codec@1.5.0':
    resolution: {integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==}

  '@jridgewell/trace-mapping@0.3.25':
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==}

  '@mjackson/node-fetch-server@0.2.0':
    resolution: {integrity: sha512-EMlH1e30yzmTpGLQjlFmaDAjyOeZhng1/XCd7DExR8PNAnG/G1tyruZxEoUe11ClnwGhGrtsdnyyUx1frSzjng==}

  '@npmcli/git@4.1.0':
    resolution: {integrity: sha512-9hwoB3gStVfa0N31ymBmrX+GuDGdVA/QWShZVqE0HK2Af+7QGGrCTbZia/SW0ImUTjTne7SP91qxDmtXvDHRPQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  '@npmcli/package-json@4.0.1':
    resolution: {integrity: sha512-lRCEGdHZomFsURroh522YvA/2cVb9oPIJrjHanCJZkiasz1BzcnLr3tBJhlV7S86MBJBuAQ33is2D60YitZL2Q==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  '@npmcli/promise-spawn@6.0.2':
    resolution: {integrity: sha512-gGq0NJkIGSwdbUt4yhdF8ZrmkGKVz9vAdVzpOfnom+V8PLSmSOVhZwbNvZZS1EYcJN5hzzKBxmmVVAInM6HQLg==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  '@pkgjs/parseargs@0.11.0':
    resolution: {integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==}
    engines: {node: '>=14'}

  '@react-router/dev@7.6.2':
    resolution: {integrity: sha512-BuG83Ug2C/P+zMYErTz/KKuXoxbOefh3oR66r13XWG9txwooC9nt2QDt2u8yt7Eo/9BATnx+TmXnOHEWqMyB8w==}
    engines: {node: '>=20.0.0'}
    hasBin: true
    peerDependencies:
      '@react-router/serve': ^7.6.2
      react-router: ^7.6.2
      typescript: ^5.1.0
      vite: ^5.1.0 || ^6.0.0
      wrangler: ^3.28.2 || ^4.0.0
    peerDependenciesMeta:
      '@react-router/serve':
        optional: true
      typescript:
        optional: true
      wrangler:
        optional: true

  '@react-router/express@7.6.2':
    resolution: {integrity: sha512-b1XwP2ZknWG6yNl1aEAJ+yx0Alk85+iLk5y521MOhh2lCKPNyFOuX4Gw8hI3E4IXgDEPqiZ+lipmrIb7XkLNZQ==}
    engines: {node: '>=20.0.0'}
    peerDependencies:
      express: ^4.17.1 || ^5
      react-router: 7.6.2
      typescript: ^5.1.0
    peerDependenciesMeta:
      typescript:
        optional: true

  '@react-router/node@7.6.2':
    resolution: {integrity: sha512-KrxfnfJVU1b+020VKemkxpc7ssItsAL8MOJthcoGwPyKwrgovdwc+8NKJUqw3P7yk/Si0ZmVh9QYAzi9qF96dg==}
    engines: {node: '>=20.0.0'}
    peerDependencies:
      react-router: 7.6.2
      typescript: ^5.1.0
    peerDependenciesMeta:
      typescript:
        optional: true

  '@react-router/serve@7.6.2':
    resolution: {integrity: sha512-VTdvB8kdZEtYeQML9TFJiIZnPefv94LfmLx5qQ0SJSesel/hQolnfpWEkLJ9WtBO+/10CulAvg6y5UwiceUFTQ==}
    engines: {node: '>=20.0.0'}
    hasBin: true
    peerDependencies:
      react-router: 7.6.2

  '@rollup/rollup-android-arm-eabi@4.43.0':
    resolution: {integrity: sha512-Krjy9awJl6rKbruhQDgivNbD1WuLb8xAclM4IR4cN5pHGAs2oIMMQJEiC3IC/9TZJ+QZkmZhlMO/6MBGxPidpw==}
    cpu: [arm]
    os: [android]

  '@rollup/rollup-android-arm64@4.43.0':
    resolution: {integrity: sha512-ss4YJwRt5I63454Rpj+mXCXicakdFmKnUNxr1dLK+5rv5FJgAxnN7s31a5VchRYxCFWdmnDWKd0wbAdTr0J5EA==}
    cpu: [arm64]
    os: [android]

  '@rollup/rollup-darwin-arm64@4.43.0':
    resolution: {integrity: sha512-eKoL8ykZ7zz8MjgBenEF2OoTNFAPFz1/lyJ5UmmFSz5jW+7XbH1+MAgCVHy72aG59rbuQLcJeiMrP8qP5d/N0A==}
    cpu: [arm64]
    os: [darwin]

  '@rollup/rollup-darwin-x64@4.43.0':
    resolution: {integrity: sha512-SYwXJgaBYW33Wi/q4ubN+ldWC4DzQY62S4Ll2dgfr/dbPoF50dlQwEaEHSKrQdSjC6oIe1WgzosoaNoHCdNuMg==}
    cpu: [x64]
    os: [darwin]

  '@rollup/rollup-freebsd-arm64@4.43.0':
    resolution: {integrity: sha512-SV+U5sSo0yujrjzBF7/YidieK2iF6E7MdF6EbYxNz94lA+R0wKl3SiixGyG/9Klab6uNBIqsN7j4Y/Fya7wAjQ==}
    cpu: [arm64]
    os: [freebsd]

  '@rollup/rollup-freebsd-x64@4.43.0':
    resolution: {integrity: sha512-J7uCsiV13L/VOeHJBo5SjasKiGxJ0g+nQTrBkAsmQBIdil3KhPnSE9GnRon4ejX1XDdsmK/l30IYLiAaQEO0Cg==}
    cpu: [x64]
    os: [freebsd]

  '@rollup/rollup-linux-arm-gnueabihf@4.43.0':
    resolution: {integrity: sha512-gTJ/JnnjCMc15uwB10TTATBEhK9meBIY+gXP4s0sHD1zHOaIh4Dmy1X9wup18IiY9tTNk5gJc4yx9ctj/fjrIw==}
    cpu: [arm]
    os: [linux]

  '@rollup/rollup-linux-arm-musleabihf@4.43.0':
    resolution: {integrity: sha512-ZJ3gZynL1LDSIvRfz0qXtTNs56n5DI2Mq+WACWZ7yGHFUEirHBRt7fyIk0NsCKhmRhn7WAcjgSkSVVxKlPNFFw==}
    cpu: [arm]
    os: [linux]

  '@rollup/rollup-linux-arm64-gnu@4.43.0':
    resolution: {integrity: sha512-8FnkipasmOOSSlfucGYEu58U8cxEdhziKjPD2FIa0ONVMxvl/hmONtX/7y4vGjdUhjcTHlKlDhw3H9t98fPvyA==}
    cpu: [arm64]
    os: [linux]

  '@rollup/rollup-linux-arm64-musl@4.43.0':
    resolution: {integrity: sha512-KPPyAdlcIZ6S9C3S2cndXDkV0Bb1OSMsX0Eelr2Bay4EsF9yi9u9uzc9RniK3mcUGCLhWY9oLr6er80P5DE6XA==}
    cpu: [arm64]
    os: [linux]

  '@rollup/rollup-linux-loongarch64-gnu@4.43.0':
    resolution: {integrity: sha512-HPGDIH0/ZzAZjvtlXj6g+KDQ9ZMHfSP553za7o2Odegb/BEfwJcR0Sw0RLNpQ9nC6Gy8s+3mSS9xjZ0n3rhcYg==}
    cpu: [loong64]
    os: [linux]

  '@rollup/rollup-linux-powerpc64le-gnu@4.43.0':
    resolution: {integrity: sha512-gEmwbOws4U4GLAJDhhtSPWPXUzDfMRedT3hFMyRAvM9Mrnj+dJIFIeL7otsv2WF3D7GrV0GIewW0y28dOYWkmw==}
    cpu: [ppc64]
    os: [linux]

  '@rollup/rollup-linux-riscv64-gnu@4.43.0':
    resolution: {integrity: sha512-XXKvo2e+wFtXZF/9xoWohHg+MuRnvO29TI5Hqe9xwN5uN8NKUYy7tXUG3EZAlfchufNCTHNGjEx7uN78KsBo0g==}
    cpu: [riscv64]
    os: [linux]

  '@rollup/rollup-linux-riscv64-musl@4.43.0':
    resolution: {integrity: sha512-ruf3hPWhjw6uDFsOAzmbNIvlXFXlBQ4nk57Sec8E8rUxs/AI4HD6xmiiasOOx/3QxS2f5eQMKTAwk7KHwpzr/Q==}
    cpu: [riscv64]
    os: [linux]

  '@rollup/rollup-linux-s390x-gnu@4.43.0':
    resolution: {integrity: sha512-QmNIAqDiEMEvFV15rsSnjoSmO0+eJLoKRD9EAa9rrYNwO/XRCtOGM3A5A0X+wmG+XRrw9Fxdsw+LnyYiZWWcVw==}
    cpu: [s390x]
    os: [linux]

  '@rollup/rollup-linux-x64-gnu@4.40.0':
    resolution: {integrity: sha512-RcDGMtqF9EFN8i2RYN2W+64CdHruJ5rPqrlYw+cgM3uOVPSsnAQps7cpjXe9be/yDp8UC7VLoCoKC8J3Kn2FkQ==}
    cpu: [x64]
    os: [linux]

  '@rollup/rollup-linux-x64-gnu@4.43.0':
    resolution: {integrity: sha512-jAHr/S0iiBtFyzjhOkAics/2SrXE092qyqEg96e90L3t9Op8OTzS6+IX0Fy5wCt2+KqeHAkti+eitV0wvblEoQ==}
    cpu: [x64]
    os: [linux]

  '@rollup/rollup-linux-x64-musl@4.43.0':
    resolution: {integrity: sha512-3yATWgdeXyuHtBhrLt98w+5fKurdqvs8B53LaoKD7P7H7FKOONLsBVMNl9ghPQZQuYcceV5CDyPfyfGpMWD9mQ==}
    cpu: [x64]
    os: [linux]

  '@rollup/rollup-win32-arm64-msvc@4.43.0':
    resolution: {integrity: sha512-wVzXp2qDSCOpcBCT5WRWLmpJRIzv23valvcTwMHEobkjippNf+C3ys/+wf07poPkeNix0paTNemB2XrHr2TnGw==}
    cpu: [arm64]
    os: [win32]

  '@rollup/rollup-win32-ia32-msvc@4.43.0':
    resolution: {integrity: sha512-fYCTEyzf8d+7diCw8b+asvWDCLMjsCEA8alvtAutqJOJp/wL5hs1rWSqJ1vkjgW0L2NB4bsYJrpKkiIPRR9dvw==}
    cpu: [ia32]
    os: [win32]

  '@rollup/rollup-win32-x64-msvc@4.43.0':
    resolution: {integrity: sha512-SnGhLiE5rlK0ofq8kzuDkM0g7FN1s5VYY+YSMTibP7CqShxCQvqtNxTARS4xX4PFJfHjG0ZQYX9iGzI3FQh5Aw==}
    cpu: [x64]
    os: [win32]

  '@swc/helpers@0.5.17':
    resolution: {integrity: sha512-5IKx/Y13RsYd+sauPb2x+U/xZikHjolzfuDgTAl/Tdf3Q8rslRvC19NKDLgAJQ6wsqADk10ntlv08nPFw/gO/A==}

  '@tailwindcss/node@4.1.10':
    resolution: {integrity: sha512-2ACf1znY5fpRBwRhMgj9ZXvb2XZW8qs+oTfotJ2C5xR0/WNL7UHZ7zXl6s+rUqedL1mNi+0O+WQr5awGowS3PQ==}

  '@tailwindcss/oxide-android-arm64@4.1.10':
    resolution: {integrity: sha512-VGLazCoRQ7rtsCzThaI1UyDu/XRYVyH4/EWiaSX6tFglE+xZB5cvtC5Omt0OQ+FfiIVP98su16jDVHDEIuH4iQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [android]

  '@tailwindcss/oxide-darwin-arm64@4.1.10':
    resolution: {integrity: sha512-ZIFqvR1irX2yNjWJzKCqTCcHZbgkSkSkZKbRM3BPzhDL/18idA8uWCoopYA2CSDdSGFlDAxYdU2yBHwAwx8euQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [darwin]

  '@tailwindcss/oxide-darwin-x64@4.1.10':
    resolution: {integrity: sha512-eCA4zbIhWUFDXoamNztmS0MjXHSEJYlvATzWnRiTqJkcUteSjO94PoRHJy1Xbwp9bptjeIxxBHh+zBWFhttbrQ==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [darwin]

  '@tailwindcss/oxide-freebsd-x64@4.1.10':
    resolution: {integrity: sha512-8/392Xu12R0cc93DpiJvNpJ4wYVSiciUlkiOHOSOQNH3adq9Gi/dtySK7dVQjXIOzlpSHjeCL89RUUI8/GTI6g==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [freebsd]

  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.10':
    resolution: {integrity: sha512-t9rhmLT6EqeuPT+MXhWhlRYIMSfh5LZ6kBrC4FS6/+M1yXwfCtp24UumgCWOAJVyjQwG+lYva6wWZxrfvB+NhQ==}
    engines: {node: '>= 10'}
    cpu: [arm]
    os: [linux]

  '@tailwindcss/oxide-linux-arm64-gnu@4.1.10':
    resolution: {integrity: sha512-3oWrlNlxLRxXejQ8zImzrVLuZ/9Z2SeKoLhtCu0hpo38hTO2iL86eFOu4sVR8cZc6n3z7eRXXqtHJECa6mFOvA==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@tailwindcss/oxide-linux-arm64-musl@4.1.10':
    resolution: {integrity: sha512-saScU0cmWvg/Ez4gUmQWr9pvY9Kssxt+Xenfx1LG7LmqjcrvBnw4r9VjkFcqmbBb7GCBwYNcZi9X3/oMda9sqQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@tailwindcss/oxide-linux-x64-gnu@4.1.10':
    resolution: {integrity: sha512-/G3ao/ybV9YEEgAXeEg28dyH6gs1QG8tvdN9c2MNZdUXYBaIY/Gx0N6RlJzfLy/7Nkdok4kaxKPHKJUlAaoTdA==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@tailwindcss/oxide-linux-x64-musl@4.1.10':
    resolution: {integrity: sha512-LNr7X8fTiKGRtQGOerSayc2pWJp/9ptRYAa4G+U+cjw9kJZvkopav1AQc5HHD+U364f71tZv6XamaHKgrIoVzA==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@tailwindcss/oxide-wasm32-wasi@4.1.10':
    resolution: {integrity: sha512-d6ekQpopFQJAcIK2i7ZzWOYGZ+A6NzzvQ3ozBvWFdeyqfOZdYHU66g5yr+/HC4ipP1ZgWsqa80+ISNILk+ae/Q==}
    engines: {node: '>=14.0.0'}
    cpu: [wasm32]
    bundledDependencies:
      - '@napi-rs/wasm-runtime'
      - '@emnapi/core'
      - '@emnapi/runtime'
      - '@tybys/wasm-util'
      - '@emnapi/wasi-threads'
      - tslib

  '@tailwindcss/oxide-win32-arm64-msvc@4.1.10':
    resolution: {integrity: sha512-i1Iwg9gRbwNVOCYmnigWCCgow8nDWSFmeTUU5nbNx3rqbe4p0kRbEqLwLJbYZKmSSp23g4N6rCDmm7OuPBXhDA==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [win32]

  '@tailwindcss/oxide-win32-x64-msvc@4.1.10':
    resolution: {integrity: sha512-sGiJTjcBSfGq2DVRtaSljq5ZgZS2SDHSIfhOylkBvHVjwOsodBhnb3HdmiKkVuUGKD0I7G63abMOVaskj1KpOA==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [win32]

  '@tailwindcss/oxide@4.1.10':
    resolution: {integrity: sha512-v0C43s7Pjw+B9w21htrQwuFObSkio2aV/qPx/mhrRldbqxbWJK6KizM+q7BF1/1CmuLqZqX3CeYF7s7P9fbA8Q==}
    engines: {node: '>= 10'}

  '@tailwindcss/vite@4.1.10':
    resolution: {integrity: sha512-QWnD5HDY2IADv+vYR82lOhqOlS1jSCUUAmfem52cXAhRTKxpDh3ARX8TTXJTCCO7Rv7cD2Nlekabv02bwP3a2A==}
    peerDependencies:
      vite: ^5.2.0 || ^6

  '@types/estree@1.0.7':
    resolution: {integrity: sha512-w28IoSUCJpidD/TGviZwwMJckNESJZXFu7NBZ5YJ4mEUnNraUn9Pm8HSZm/jDF1pDWYKspWE7oVphigUPRakIQ==}

  '@types/node@20.19.0':
    resolution: {integrity: sha512-hfrc+1tud1xcdVTABC2JiomZJEklMcXYNTVtZLAeqTVWD+qL5jkHKT+1lOtqDdGxt+mB53DTtiz673vfjU8D1Q==}

  '@types/prop-types@15.7.15':
    resolution: {integrity: sha512-F6bEyamV9jKGAFBEmlQnesRPGOQqS2+Uwi0Em15xenOxHaf2hv6L8YCVn3rPdPJOiJfPiCnLIRyvwVaqMY3MIw==}

  '@types/react-dom@18.3.7':
    resolution: {integrity: sha512-MEe3UeoENYVFXzoXEWsvcpg6ZvlrFNlOQ7EOsvhI3CfAXwzPfO8Qwuxd40nepsYKqyyVQnTdEfv68q91yLcKrQ==}
    peerDependencies:
      '@types/react': ^18.0.0

  '@types/react-dom@19.1.6':
    resolution: {integrity: sha512-4hOiT/dwO8Ko0gV1m/TJZYk3y0KBnY9vzDh7W+DH17b2HFSOGgdj33dhihPeuy3l0q23+4e+hoXHV6hCC4dCXw==}
    peerDependencies:
      '@types/react': ^19.0.0

  '@types/react@18.3.23':
    resolution: {integrity: sha512-/LDXMQh55EzZQ0uVAZmKKhfENivEvWz6E+EYzh+/MCjMhNsotd+ZHhBGIjFDTi6+fz0OhQQQLbTgdQIxxCsC0w==}

  '@types/react@19.1.8':
    resolution: {integrity: sha512-AwAfQ2Wa5bCx9WP8nZL2uMZWod7J7/JSplxbTmBQ5ms6QpqNYm672H0Vu9ZVKVngQ+ii4R/byguVEUZQyeg44g==}

  accepts@1.3.8:
    resolution: {integrity: sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==}
    engines: {node: '>= 0.6'}

  ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  ansi-regex@6.1.0:
    resolution: {integrity: sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==}
    engines: {node: '>=12'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}

  ansi-styles@6.2.1:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==}
    engines: {node: '>=12'}

  arg@5.0.2:
    resolution: {integrity: sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==}

  array-flatten@1.1.1:
    resolution: {integrity: sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==}

  babel-dead-code-elimination@1.0.10:
    resolution: {integrity: sha512-DV5bdJZTzZ0zn0DC24v3jD7Mnidh6xhKa4GfKCbq3sfW8kaWhDdZjP3i81geA8T33tdYqWKw4D3fVv0CwEgKVA==}

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  basic-auth@2.0.1:
    resolution: {integrity: sha512-NF+epuEdnUYVlGuhaxbbq+dvJttwLnGY+YixlXlME5KpQ5W3CnXA5cVTneY3SPbPDRkcjMbifrwmFYcClgOZeg==}
    engines: {node: '>= 0.8'}

  body-parser@1.20.3:
    resolution: {integrity: sha512-7rAxByjUMqQ3/bHJy7D6OGXvx/MMc4IqBn/X0fcM1QUcAItpZrBEYhWGem+tzXH90c+G01ypMcYJBO9Y30203g==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  brace-expansion@2.0.2:
    resolution: {integrity: sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==}

  browserslist@4.25.0:
    resolution: {integrity: sha512-PJ8gYKeS5e/whHBh8xrwYK+dAvEj7JXtz6uTucnMRB8OiGTsKccFekoRrjajPBHV8oOY+2tI4uxeceSimKwMFA==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}

  bytes@3.1.2:
    resolution: {integrity: sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==}
    engines: {node: '>= 0.8'}

  cac@6.7.14:
    resolution: {integrity: sha512-b6Ilus+c3RrdDk+JhLKUAQfzzgLEPy6wcXqS7f/xe1EETvsDP6GORG7SFuOs6cID5YkqchW/LXZbX5bc8j7ZcQ==}
    engines: {node: '>=8'}

  call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==}
    engines: {node: '>= 0.4'}

  call-bound@1.0.4:
    resolution: {integrity: sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==}
    engines: {node: '>= 0.4'}

  caniuse-lite@1.0.30001723:
    resolution: {integrity: sha512-1R/elMjtehrFejxwmexeXAtae5UO9iSyFn6G/I806CYC/BLyyBk1EPhrKBkWhy6wM6Xnm47dSJQec+tLJ39WHw==}

  chokidar@4.0.3:
    resolution: {integrity: sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA==}
    engines: {node: '>= 14.16.0'}

  chownr@3.0.0:
    resolution: {integrity: sha512-+IxzY9BZOQd/XuYPRmrvEVjF/nqj5kgT4kEq7VofrDoM1MxoRjEWkrCC3EtLi59TVawxTAn+orJwFQcrqEN1+g==}
    engines: {node: '>=18'}

  color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}

  color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  compressible@2.0.18:
    resolution: {integrity: sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==}
    engines: {node: '>= 0.6'}

  compression@1.8.0:
    resolution: {integrity: sha512-k6WLKfunuqCYD3t6AsuPGvQWaKwuLLh2/xHNcX4qE+vIfDNXpSqnrhwA7O53R7WVQUnt8dVAIW+YHr7xTgOgGA==}
    engines: {node: '>= 0.8.0'}

  content-disposition@0.5.4:
    resolution: {integrity: sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==}
    engines: {node: '>= 0.6'}

  content-type@1.0.5:
    resolution: {integrity: sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==}
    engines: {node: '>= 0.6'}

  convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==}

  cookie-signature@1.0.6:
    resolution: {integrity: sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==}

  cookie@0.7.1:
    resolution: {integrity: sha512-6DnInpx7SJ2AK3+CTUE/ZM0vWTUboZCegxhC2xiIydHR9jNuTAASBrfEpHhiGOZw/nX51bHt6YQl8jsGo4y/0w==}
    engines: {node: '>= 0.6'}

  cookie@1.0.2:
    resolution: {integrity: sha512-9Kr/j4O16ISv8zBBhJoi4bXOYNTkFLOqSL3UDB0njXxCXNezjeyVrJyGOWtgfs/q2km1gwBcfH8q1yEGoMYunA==}
    engines: {node: '>=18'}

  cross-spawn@7.0.6:
    resolution: {integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==}
    engines: {node: '>= 8'}

  csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  debug@2.6.9:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.4.1:
    resolution: {integrity: sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  dedent@1.6.0:
    resolution: {integrity: sha512-F1Z+5UCFpmQUzJa11agbyPVMbpgT/qA3/SKyJ1jyBgm7dUcUEa8v9JwDkerSQXfakBwFljIxhOJqGkjUwZ9FSA==}
    peerDependencies:
      babel-plugin-macros: ^3.1.0
    peerDependenciesMeta:
      babel-plugin-macros:
        optional: true

  depd@2.0.0:
    resolution: {integrity: sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==}
    engines: {node: '>= 0.8'}

  destroy@1.2.0:
    resolution: {integrity: sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  detect-libc@2.0.4:
    resolution: {integrity: sha512-3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA==}
    engines: {node: '>=8'}

  dom-helpers@5.2.1:
    resolution: {integrity: sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==}

  dunder-proto@1.0.1:
    resolution: {integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==}
    engines: {node: '>= 0.4'}

  eastasianwidth@0.2.0:
    resolution: {integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==}

  ee-first@1.1.1:
    resolution: {integrity: sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==}

  electron-to-chromium@1.5.167:
    resolution: {integrity: sha512-LxcRvnYO5ez2bMOFpbuuVuAI5QNeY1ncVytE/KXaL6ZNfzX1yPlAO0nSOyIHx2fVAuUprMqPs/TdVhUFZy7SIQ==}

  embla-carousel-autoplay@8.6.0:
    resolution: {integrity: sha512-OBu5G3nwaSXkZCo1A6LTaFMZ8EpkYbwIaH+bPqdBnDGQ2fh4+NbzjXjs2SktoPNKCtflfVMc75njaDHOYXcrsA==}
    peerDependencies:
      embla-carousel: 8.6.0

  embla-carousel-fade@8.6.0:
    resolution: {integrity: sha512-qaYsx5mwCz72ZrjlsXgs1nKejSrW+UhkbOMwLgfRT7w2LtdEB03nPRI06GHuHv5ac2USvbEiX2/nAHctcDwvpg==}
    peerDependencies:
      embla-carousel: 8.6.0

  embla-carousel@8.6.0:
    resolution: {integrity: sha512-SjWyZBHJPbqxHOzckOfo8lHisEaJWmwd23XppYFYVh10bU66/Pn5tkVkbkCMZVdbUE5eTCI2nD8OyIP4Z+uwkA==}

  emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  emoji-regex@9.2.2:
    resolution: {integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==}

  encodeurl@1.0.2:
    resolution: {integrity: sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==}
    engines: {node: '>= 0.8'}

  encodeurl@2.0.0:
    resolution: {integrity: sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==}
    engines: {node: '>= 0.8'}

  enhanced-resolve@5.18.1:
    resolution: {integrity: sha512-ZSW3ma5GkcQBIpwZTSRAI8N71Uuwgs93IezB7mf7R60tC8ZbJideoDNKjHn2O9KIlx6rkGTTEk1xUCK2E1Y2Yg==}
    engines: {node: '>=10.13.0'}

  err-code@2.0.3:
    resolution: {integrity: sha512-2bmlRpNKBxT/CRmPOlyISQpNj+qSeYvcym/uT0Jx2bMOlKLtSy1ZmLuVxSEKKyor/N5yhvp/ZiG1oE3DEYMSFA==}

  es-define-property@1.0.1:
    resolution: {integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  es-module-lexer@1.7.0:
    resolution: {integrity: sha512-jEQoCwk8hyb2AZziIOLhDqpm5+2ww5uIE6lkO/6jcOCusfk6LhMHpXXfBLXTZ7Ydyt0j4VoUQv6uGNYbdW+kBA==}

  es-object-atoms@1.1.1:
    resolution: {integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==}
    engines: {node: '>= 0.4'}

  esbuild@0.25.5:
    resolution: {integrity: sha512-P8OtKZRv/5J5hhz0cUAdu/cLuPIKXpQl1R9pZtvmHWQvrAUVd0UNIPT4IB4W3rNOqVO0rlqHmCIbSwxh/c9yUQ==}
    engines: {node: '>=18'}
    hasBin: true

  escalade@3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==}
    engines: {node: '>=6'}

  escape-html@1.0.3:
    resolution: {integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==}

  etag@1.8.1:
    resolution: {integrity: sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==}
    engines: {node: '>= 0.6'}

  exit-hook@2.2.1:
    resolution: {integrity: sha512-eNTPlAD67BmP31LDINZ3U7HSF8l57TxOY2PmBJ1shpCvpnxBF93mWCE8YHBnXs8qiUZJc9WDcWIeC3a2HIAMfw==}
    engines: {node: '>=6'}

  express@4.21.2:
    resolution: {integrity: sha512-28HqgMZAmih1Czt9ny7qr6ek2qddF4FclbMzwhCREB6OFfH+rXAnuNCwo1/wFvrtbgsQDb4kSbX9de9lFbrXnA==}
    engines: {node: '>= 0.10.0'}

  fdir@6.4.6:
    resolution: {integrity: sha512-hiFoqpyZcfNm1yc4u8oWCf9A2c4D3QjCrks3zmoVKVxpQRzmPNar1hUJcBG2RQHvEVGDN+Jm81ZheVLAQMK6+w==}
    peerDependencies:
      picomatch: ^3 || ^4
    peerDependenciesMeta:
      picomatch:
        optional: true

  finalhandler@1.3.1:
    resolution: {integrity: sha512-6BN9trH7bp3qvnrRyzsBz+g3lZxTNZTbVO2EV1CS0WIcDbawYVdYvGflME/9QP0h0pYlCDBCTjYa9nZzMDpyxQ==}
    engines: {node: '>= 0.8'}

  foreground-child@3.3.1:
    resolution: {integrity: sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==}
    engines: {node: '>=14'}

  forwarded@0.2.0:
    resolution: {integrity: sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==}
    engines: {node: '>= 0.6'}

  fresh@0.5.2:
    resolution: {integrity: sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==}
    engines: {node: '>= 0.6'}

  fs-extra@10.1.0:
    resolution: {integrity: sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==}
    engines: {node: '>=12'}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}

  get-intrinsic@1.3.0:
    resolution: {integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==}
    engines: {node: '>= 0.4'}

  get-port@5.1.1:
    resolution: {integrity: sha512-g/Q1aTSDOxFpchXC4i8ZWvxA1lnPqx/JHqcpIw0/LX9T8x/GBbi6YnlN5nhaKIFkT8oFsscUKgDJYxfwfS6QsQ==}
    engines: {node: '>=8'}

  get-proto@1.0.1:
    resolution: {integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==}
    engines: {node: '>= 0.4'}

  glob@10.4.5:
    resolution: {integrity: sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==}
    hasBin: true

  globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}

  globrex@0.1.2:
    resolution: {integrity: sha512-uHJgbwAMwNFf5mLst7IWLNg14x1CkeqglJb/K3doi4dw6q2IvAAmM/Y81kevy83wP+Sst+nutFTYOGg3d1lsxg==}

  gopd@1.2.0:
    resolution: {integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==}
    engines: {node: '>= 0.4'}

  graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  has-symbols@1.1.0:
    resolution: {integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==}
    engines: {node: '>= 0.4'}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  hosted-git-info@6.1.3:
    resolution: {integrity: sha512-HVJyzUrLIL1c0QmviVh5E8VGyUS7xCFPS6yydaVd1UegW+ibV/CohqTH9MkOLDp5o+rb82DMo77PTuc9F/8GKw==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  http-errors@2.0.0:
    resolution: {integrity: sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==}
    engines: {node: '>= 0.8'}

  iconv-lite@0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==}
    engines: {node: '>=0.10.0'}

  inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  ipaddr.js@1.9.1:
    resolution: {integrity: sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==}
    engines: {node: '>= 0.10'}

  is-core-module@2.16.1:
    resolution: {integrity: sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==}
    engines: {node: '>= 0.4'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  isbot@5.1.28:
    resolution: {integrity: sha512-qrOp4g3xj8YNse4biorv6O5ZShwsJM0trsoda4y7j/Su7ZtTTfVXFzbKkpgcSoDrHS8FcTuUwcU04YimZlZOxw==}
    engines: {node: '>=18'}

  isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  jackspeak@3.4.3:
    resolution: {integrity: sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==}

  jiti@2.4.2:
    resolution: {integrity: sha512-rg9zJN+G4n2nfJl5MW3BMygZX56zKPNVEYYqq7adpmMh4Jn2QNEwhvQlFy6jPVdcod7txZtKHWnyZiA3a0zP7A==}
    hasBin: true

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  jsesc@3.0.2:
    resolution: {integrity: sha512-xKqzzWXDttJuOcawBt4KnKHHIf5oQ/Cxax+0PWFG+DFDgHNAdi+TXECADI+RYiFUMmx8792xsMbbgXj4CwnP4g==}
    engines: {node: '>=6'}
    hasBin: true

  json-parse-even-better-errors@3.0.2:
    resolution: {integrity: sha512-fi0NG4bPjCHunUJffmLd0gxssIgkNmArMvis4iNah6Owg1MCJjWhEcDLmsK6iGkJq3tHwbDkTlce70/tmXN4cQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true

  jsonfile@6.1.0:
    resolution: {integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==}

  keyborg@2.6.0:
    resolution: {integrity: sha512-o5kvLbuTF+o326CMVYpjlaykxqYP9DphFQZ2ZpgrvBouyvOxyEB7oqe8nOLFpiV5VCtz0D3pt8gXQYWpLpBnmA==}

  lightningcss-darwin-arm64@1.30.1:
    resolution: {integrity: sha512-c8JK7hyE65X1MHMN+Viq9n11RRC7hgin3HhYKhrMyaXflk5GVplZ60IxyoVtzILeKr+xAJwg6zK6sjTBJ0FKYQ==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [darwin]

  lightningcss-darwin-x64@1.30.1:
    resolution: {integrity: sha512-k1EvjakfumAQoTfcXUcHQZhSpLlkAuEkdMBsI/ivWw9hL+7FtilQc0Cy3hrx0AAQrVtQAbMI7YjCgYgvn37PzA==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [darwin]

  lightningcss-freebsd-x64@1.30.1:
    resolution: {integrity: sha512-kmW6UGCGg2PcyUE59K5r0kWfKPAVy4SltVeut+umLCFoJ53RdCUWxcRDzO1eTaxf/7Q2H7LTquFHPL5R+Gjyig==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [freebsd]

  lightningcss-linux-arm-gnueabihf@1.30.1:
    resolution: {integrity: sha512-MjxUShl1v8pit+6D/zSPq9S9dQ2NPFSQwGvxBCYaBYLPlCWuPh9/t1MRS8iUaR8i+a6w7aps+B4N0S1TYP/R+Q==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm]
    os: [linux]

  lightningcss-linux-arm64-gnu@1.30.1:
    resolution: {integrity: sha512-gB72maP8rmrKsnKYy8XUuXi/4OctJiuQjcuqWNlJQ6jZiWqtPvqFziskH3hnajfvKB27ynbVCucKSm2rkQp4Bw==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [linux]

  lightningcss-linux-arm64-musl@1.30.1:
    resolution: {integrity: sha512-jmUQVx4331m6LIX+0wUhBbmMX7TCfjF5FoOH6SD1CttzuYlGNVpA7QnrmLxrsub43ClTINfGSYyHe2HWeLl5CQ==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [linux]

  lightningcss-linux-x64-gnu@1.30.1:
    resolution: {integrity: sha512-piWx3z4wN8J8z3+O5kO74+yr6ze/dKmPnI7vLqfSqI8bccaTGY5xiSGVIJBDd5K5BHlvVLpUB3S2YCfelyJ1bw==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [linux]

  lightningcss-linux-x64-musl@1.30.1:
    resolution: {integrity: sha512-rRomAK7eIkL+tHY0YPxbc5Dra2gXlI63HL+v1Pdi1a3sC+tJTcFrHX+E86sulgAXeI7rSzDYhPSeHHjqFhqfeQ==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [linux]

  lightningcss-win32-arm64-msvc@1.30.1:
    resolution: {integrity: sha512-mSL4rqPi4iXq5YVqzSsJgMVFENoa4nGTT/GjO2c0Yl9OuQfPsIfncvLrEW6RbbB24WtZ3xP/2CCmI3tNkNV4oA==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [win32]

  lightningcss-win32-x64-msvc@1.30.1:
    resolution: {integrity: sha512-PVqXh48wh4T53F/1CCu8PIPCxLzWyCnn/9T5W1Jpmdy5h9Cwd+0YQS6/LwhHXSafuc61/xg9Lv5OrCby6a++jg==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [win32]

  lightningcss@1.30.1:
    resolution: {integrity: sha512-xi6IyHML+c9+Q3W0S4fCQJOym42pyurFiJUHEcEyHS0CeKzia4yZDEsLlqOFykxOdHpNy0NmvVO31vcSqAxJCg==}
    engines: {node: '>= 12.0.0'}

  lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  loose-envify@1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true

  lru-cache@10.4.3:
    resolution: {integrity: sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==}

  lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}

  lru-cache@7.18.3:
    resolution: {integrity: sha512-jumlc0BIUrS3qJGgIkWZsyfAM7NCWiBcCDhnd+3NNM5KbBmLTgHVfWBcg6W+rLUsIpzpERPsvwUP7CckAQSOoA==}
    engines: {node: '>=12'}

  magic-string@0.30.17:
    resolution: {integrity: sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==}

  math-intrinsics@1.1.0:
    resolution: {integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==}
    engines: {node: '>= 0.4'}

  media-typer@0.3.0:
    resolution: {integrity: sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==}
    engines: {node: '>= 0.6'}

  merge-descriptors@1.0.3:
    resolution: {integrity: sha512-gaNvAS7TZ897/rVaZ0nMtAyxNyi/pdbjbAwUpFQpN70GqnVfOiXpeUUMKRBmzXaSQ8DdTX4/0ms62r2K+hE6mQ==}

  methods@1.1.2:
    resolution: {integrity: sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==}
    engines: {node: '>= 0.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-db@1.54.0:
    resolution: {integrity: sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  mime@1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==}
    engines: {node: '>=4'}
    hasBin: true

  minimatch@9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==}
    engines: {node: '>=16 || 14 >=14.17'}

  minipass@7.1.2:
    resolution: {integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==}
    engines: {node: '>=16 || 14 >=14.17'}

  minizlib@3.0.2:
    resolution: {integrity: sha512-oG62iEk+CYt5Xj2YqI5Xi9xWUeZhDI8jjQmC5oThVH5JGCTgIjr7ciJDzC7MBzYd//WvR1OTmP5Q38Q8ShQtVA==}
    engines: {node: '>= 18'}

  mkdirp@3.0.1:
    resolution: {integrity: sha512-+NsyUUAZDmo6YVHzL/stxSu3t9YS1iljliy3BSDrXJ/dkn1KYdmtZODGGjLcc9XLgVVpH4KshHB8XmZgMhaBXg==}
    engines: {node: '>=10'}
    hasBin: true

  morgan@1.10.0:
    resolution: {integrity: sha512-AbegBVI4sh6El+1gNwvD5YIck7nSA36weD7xvIxG4in80j/UoK8AEGaWnnz8v1GxonMCltmlNs5ZKbGvl9b1XQ==}
    engines: {node: '>= 0.8.0'}

  ms@2.0.0:
    resolution: {integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  nanoid@3.3.11:
    resolution: {integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  negotiator@0.6.3:
    resolution: {integrity: sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==}
    engines: {node: '>= 0.6'}

  negotiator@0.6.4:
    resolution: {integrity: sha512-myRT3DiWPHqho5PrJaIRyaMv2kgYf0mUVgBNOYMuCH5Ki1yEiQaf/ZJuQ62nvpc44wL5WDbTX7yGJi1Neevw8w==}
    engines: {node: '>= 0.6'}

  node-releases@2.0.19:
    resolution: {integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==}

  normalize-package-data@5.0.0:
    resolution: {integrity: sha512-h9iPVIfrVZ9wVYQnxFgtw1ugSvGEMOlyPWWtm8BMJhnwyEL/FLbYbTY3V3PpjI/BUK67n9PEWDu6eHzu1fB15Q==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  npm-install-checks@6.3.0:
    resolution: {integrity: sha512-W29RiK/xtpCGqn6f3ixfRYGk+zRyr+Ew9F2E20BfXxT5/euLdA/Nm7fO7OeTGuAmTs30cpgInyJ0cYe708YTZw==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  npm-normalize-package-bin@3.0.1:
    resolution: {integrity: sha512-dMxCf+zZ+3zeQZXKxmyuCKlIDPGuv8EF940xbkC4kQVDTtqoh6rJFO+JTKSA6/Rwi0getWmtuy4Itup0AMcaDQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  npm-package-arg@10.1.0:
    resolution: {integrity: sha512-uFyyCEmgBfZTtrKk/5xDfHp6+MdrqGotX/VoOyEEl3mBwiEE5FlBaePanazJSVMPT7vKepcjYBY2ztg9A3yPIA==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  npm-pick-manifest@8.0.2:
    resolution: {integrity: sha512-1dKY+86/AIiq1tkKVD3l0WI+Gd3vkknVGAggsFeBkTvbhMQ1OND/LKkYv4JtXPKUJ8bOTCyLiqEg2P6QNdK+Gg==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  object-inspect@1.13.4:
    resolution: {integrity: sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==}
    engines: {node: '>= 0.4'}

  on-finished@2.3.0:
    resolution: {integrity: sha512-ikqdkGAAyf/X/gPhXGvfgAytDZtDbr+bkNUJ0N9h5MI/dmdgCs3l6hoHrcUv41sRKew3jIwrp4qQDXiK99Utww==}
    engines: {node: '>= 0.8'}

  on-finished@2.4.1:
    resolution: {integrity: sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==}
    engines: {node: '>= 0.8'}

  on-headers@1.0.2:
    resolution: {integrity: sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==}
    engines: {node: '>= 0.8'}

  package-json-from-dist@1.0.1:
    resolution: {integrity: sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==}

  parseurl@1.3.3:
    resolution: {integrity: sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==}
    engines: {node: '>= 0.8'}

  path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  path-scurry@1.11.1:
    resolution: {integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==}
    engines: {node: '>=16 || 14 >=14.18'}

  path-to-regexp@0.1.12:
    resolution: {integrity: sha512-RA1GjUVMnvYFxuqovrEqZoxxW5NUZqbwKtYz/Tt7nXerk0LbLblQmrsgdeOxV5SFHf0UDggjS/bSeOZwt1pmEQ==}

  pathe@1.1.2:
    resolution: {integrity: sha512-whLdWMYL2TwI08hn8/ZqAbrVemu0LNaNNJZX73O6qaIdCTfXutsLhMkjdENX0qhsQ9uIimo4/aQOmXkoon2nDQ==}

  pathe@2.0.3:
    resolution: {integrity: sha512-WUjGcAqP1gQacoQe+OBJsFA7Ld4DyXuUIjZ5cc75cLHvJ7dtNsTugphxIADwspS+AraAUePCKrSVtPLFj/F88w==}

  picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  picomatch@4.0.2:
    resolution: {integrity: sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==}
    engines: {node: '>=12'}

  postcss@8.5.5:
    resolution: {integrity: sha512-d/jtm+rdNT8tpXuHY5MMtcbJFBkhXE6593XVR9UoGCH8jSFGci7jGvMGH5RYd5PBJW+00NZQt6gf7CbagJCrhg==}
    engines: {node: ^10 || ^12 || >=14}

  prettier@2.8.8:
    resolution: {integrity: sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q==}
    engines: {node: '>=10.13.0'}
    hasBin: true

  proc-log@3.0.0:
    resolution: {integrity: sha512-++Vn7NS4Xf9NacaU9Xq3URUuqZETPsf8L4j5/ckhaRYsfPeRyzGw+iDjFhV/Jr3uNmTvvddEJFWh5R1gRgUH8A==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  promise-inflight@1.0.1:
    resolution: {integrity: sha512-6zWPyEOFaQBJYcGMHBKTKJ3u6TBsnMFOIZSa6ce1e/ZrrsOlnHRHbabMjLiBYKp+n44X9eUI6VUPaukCXHuG4g==}
    peerDependencies:
      bluebird: '*'
    peerDependenciesMeta:
      bluebird:
        optional: true

  promise-retry@2.0.1:
    resolution: {integrity: sha512-y+WKFlBR8BGXnsNlIHFGPZmyDf3DFMoLhaflAnyZgV6rG6xu+JwesTo2Q9R6XwYmtmwAFCkAk3e35jEdoeh/3g==}
    engines: {node: '>=10'}

  prop-types@15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==}

  proxy-addr@2.0.7:
    resolution: {integrity: sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==}
    engines: {node: '>= 0.10'}

  qs@6.13.0:
    resolution: {integrity: sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg==}
    engines: {node: '>=0.6'}

  range-parser@1.2.1:
    resolution: {integrity: sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==}
    engines: {node: '>= 0.6'}

  raw-body@2.5.2:
    resolution: {integrity: sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==}
    engines: {node: '>= 0.8'}

  react-dom@18.3.1:
    resolution: {integrity: sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==}
    peerDependencies:
      react: ^18.3.1

  react-is@16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}

  react-is@17.0.2:
    resolution: {integrity: sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w==}

  react-refresh@0.14.2:
    resolution: {integrity: sha512-jCvmsr+1IUSMUyzOkRcvnVbX3ZYC6g9TDrDbFuFmRDq7PD4yaGbLKNQL6k2jnArV8hjYxh7hVhAZB6s9HDGpZA==}
    engines: {node: '>=0.10.0'}

  react-router@7.6.2:
    resolution: {integrity: sha512-U7Nv3y+bMimgWjhlT5CRdzHPu2/KVmqPwKUCChW8en5P3znxUqwlYFlbmyj8Rgp1SF6zs5X4+77kBVknkg6a0w==}
    engines: {node: '>=20.0.0'}
    peerDependencies:
      react: '>=18'
      react-dom: '>=18'
    peerDependenciesMeta:
      react-dom:
        optional: true

  react-transition-group@4.4.5:
    resolution: {integrity: sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==}
    peerDependencies:
      react: '>=16.6.0'
      react-dom: '>=16.6.0'

  react@18.3.1:
    resolution: {integrity: sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==}
    engines: {node: '>=0.10.0'}

  readdirp@4.1.2:
    resolution: {integrity: sha512-GDhwkLfywWL2s6vEjyhri+eXmfH6j1L7JE27WhqLeYzoh/A3DBaYGEj2H/HFZCn/kMfim73FXxEJTw06WtxQwg==}
    engines: {node: '>= 14.18.0'}

  retry@0.12.0:
    resolution: {integrity: sha512-9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow==}
    engines: {node: '>= 4'}

  rollup@4.43.0:
    resolution: {integrity: sha512-wdN2Kd3Twh8MAEOEJZsuxuLKCsBEo4PVNLK6tQWAn10VhsVewQLzcucMgLolRlhFybGxfclbPeEYBaP6RvUFGg==}
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true

  rtl-css-js@1.16.1:
    resolution: {integrity: sha512-lRQgou1mu19e+Ya0LsTvKrVJ5TYUbqCVPAiImX3UfLTenarvPUl1QFdvu5Z3PYmHT9RCcwIfbjRQBntExyj3Zg==}

  safe-buffer@5.1.2:
    resolution: {integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==}

  safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  scheduler@0.23.2:
    resolution: {integrity: sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==}

  semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true

  semver@7.7.2:
    resolution: {integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==}
    engines: {node: '>=10'}
    hasBin: true

  send@0.19.0:
    resolution: {integrity: sha512-dW41u5VfLXu8SJh5bwRmyYUbAoSB3c9uQh6L8h/KtsFREPWpbX1lrljJo186Jc4nmci/sGUZ9a0a0J2zgfq2hw==}
    engines: {node: '>= 0.8.0'}

  serve-static@1.16.2:
    resolution: {integrity: sha512-VqpjJZKadQB/PEbEwvFdO43Ax5dFBZ2UECszz8bQ7pi7wt//PWe1P6MN7eCnjsatYtBT6EuiClbjSWP2WrIoTw==}
    engines: {node: '>= 0.8.0'}

  set-cookie-parser@2.7.1:
    resolution: {integrity: sha512-IOc8uWeOZgnb3ptbCURJWNjWUPcO3ZnTTdzsurqERrP6nPyv+paC55vJM0LpOlT2ne+Ix+9+CRG1MNLlyZ4GjQ==}

  setprototypeof@1.2.0:
    resolution: {integrity: sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==}

  shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  side-channel-list@1.0.0:
    resolution: {integrity: sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==}
    engines: {node: '>= 0.4'}

  side-channel-map@1.0.1:
    resolution: {integrity: sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==}
    engines: {node: '>= 0.4'}

  side-channel-weakmap@1.0.2:
    resolution: {integrity: sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==}
    engines: {node: '>= 0.4'}

  side-channel@1.1.0:
    resolution: {integrity: sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==}
    engines: {node: '>= 0.4'}

  signal-exit@4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==}
    engines: {node: '>=14'}

  source-map-js@1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}

  source-map-support@0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==}

  source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  spdx-correct@3.2.0:
    resolution: {integrity: sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA==}

  spdx-exceptions@2.5.0:
    resolution: {integrity: sha512-PiU42r+xO4UbUS1buo3LPJkjlO7430Xn5SVAhdpzzsPHsjbYVflnnFdATgabnLude+Cqu25p6N+g2lw/PFsa4w==}

  spdx-expression-parse@3.0.1:
    resolution: {integrity: sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==}

  spdx-license-ids@3.0.21:
    resolution: {integrity: sha512-Bvg/8F5XephndSK3JffaRqdT+gyhfqIPwDHpX80tJrF8QQRYMo8sNMeaZ2Dp5+jhwKnUmIOyFFQfHRkjJm5nXg==}

  statuses@2.0.1:
    resolution: {integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==}
    engines: {node: '>= 0.8'}

  stream-slice@0.1.2:
    resolution: {integrity: sha512-QzQxpoacatkreL6jsxnVb7X5R/pGw9OUv2qWTYWnmLpg4NdN31snPy/f3TdQE1ZUXaThRvj1Zw4/OGg0ZkaLMA==}

  string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}

  string-width@5.1.2:
    resolution: {integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==}
    engines: {node: '>=12'}

  strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}

  strip-ansi@7.1.0:
    resolution: {integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==}
    engines: {node: '>=12'}

  stylis@4.3.6:
    resolution: {integrity: sha512-yQ3rwFWRfwNUY7H5vpU0wfdkNSnvnJinhF9830Swlaxl03zsOjCfmX0ugac+3LtK0lYSgwL/KXc8oYL3mG4YFQ==}

  tabster@8.5.6:
    resolution: {integrity: sha512-2vfrRGrx8O9BjdrtSlVA5fvpmbq5HQBRN13XFRg6LAvZ1Fr3QdBnswgT4YgFS5Bhoo5nxwgjRaRueI2Us/dv7g==}

  tailwindcss@4.1.10:
    resolution: {integrity: sha512-P3nr6WkvKV/ONsTzj6Gb57sWPMX29EPNPopo7+FcpkQaNsrNpZ1pv8QmrYI2RqEKD7mlGqLnGovlcYnBK0IqUA==}

  tapable@2.2.2:
    resolution: {integrity: sha512-Re10+NauLTMCudc7T5WLFLAwDhQ0JWdrMK+9B2M8zR5hRExKmsRDCBA7/aV/pNJFltmBFO5BAMlQFi/vq3nKOg==}
    engines: {node: '>=6'}

  tar@7.4.3:
    resolution: {integrity: sha512-5S7Va8hKfV7W5U6g3aYxXmlPoZVAwUMy9AOKyF2fVuZa2UD3qZjg578OrLRt8PcNN1PleVaL/5/yYATNL0ICUw==}
    engines: {node: '>=18'}

  tinyglobby@0.2.14:
    resolution: {integrity: sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ==}
    engines: {node: '>=12.0.0'}

  toidentifier@1.0.1:
    resolution: {integrity: sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==}
    engines: {node: '>=0.6'}

  tsconfck@3.1.6:
    resolution: {integrity: sha512-ks6Vjr/jEw0P1gmOVwutM3B7fWxoWBL2KRDb1JfqGVawBmO5UsvmWOQFGHBPl5yxYz4eERr19E6L7NMv+Fej4w==}
    engines: {node: ^18 || >=20}
    hasBin: true
    peerDependencies:
      typescript: ^5.0.0
    peerDependenciesMeta:
      typescript:
        optional: true

  tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}

  turbo-darwin-64@2.5.4:
    resolution: {integrity: sha512-ah6YnH2dErojhFooxEzmvsoZQTMImaruZhFPfMKPBq8sb+hALRdvBNLqfc8NWlZq576FkfRZ/MSi4SHvVFT9PQ==}
    cpu: [x64]
    os: [darwin]

  turbo-darwin-arm64@2.5.4:
    resolution: {integrity: sha512-2+Nx6LAyuXw2MdXb7pxqle3MYignLvS7OwtsP9SgtSBaMlnNlxl9BovzqdYAgkUW3AsYiQMJ/wBRb7d+xemM5A==}
    cpu: [arm64]
    os: [darwin]

  turbo-linux-64@2.5.4:
    resolution: {integrity: sha512-5May2kjWbc8w4XxswGAl74GZ5eM4Gr6IiroqdLhXeXyfvWEdm2mFYCSWOzz0/z5cAgqyGidF1jt1qzUR8hTmOA==}
    cpu: [x64]
    os: [linux]

  turbo-linux-arm64@2.5.4:
    resolution: {integrity: sha512-/2yqFaS3TbfxV3P5yG2JUI79P7OUQKOUvAnx4MV9Bdz6jqHsHwc9WZPpO4QseQm+NvmgY6ICORnoVPODxGUiJg==}
    cpu: [arm64]
    os: [linux]

  turbo-windows-64@2.5.4:
    resolution: {integrity: sha512-EQUO4SmaCDhO6zYohxIjJpOKRN3wlfU7jMAj3CgcyTPvQR/UFLEKAYHqJOnJtymbQmiiM/ihX6c6W6Uq0yC7mA==}
    cpu: [x64]
    os: [win32]

  turbo-windows-arm64@2.5.4:
    resolution: {integrity: sha512-oQ8RrK1VS8lrxkLriotFq+PiF7iiGgkZtfLKF4DDKsmdbPo0O9R2mQxm7jHLuXraRCuIQDWMIw6dpcr7Iykf4A==}
    cpu: [arm64]
    os: [win32]

  turbo@2.5.4:
    resolution: {integrity: sha512-kc8ZibdRcuWUG1pbYSBFWqmIjynlD8Lp7IB6U3vIzvOv9VG+6Sp8bzyeBWE3Oi8XV5KsQrznyRTBPvrf99E4mA==}
    hasBin: true

  type-is@1.6.18:
    resolution: {integrity: sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==}
    engines: {node: '>= 0.6'}

  typescript@5.8.3:
    resolution: {integrity: sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==}
    engines: {node: '>=14.17'}
    hasBin: true

  undici-types@6.21.0:
    resolution: {integrity: sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==}

  undici@6.21.3:
    resolution: {integrity: sha512-gBLkYIlEnSp8pFbT64yFgGE6UIB9tAkhukC23PmMDCe5Nd+cRqKxSjw5y54MK2AZMgZfJWMaNE4nYUHgi1XEOw==}
    engines: {node: '>=18.17'}

  universalify@2.0.1:
    resolution: {integrity: sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==}
    engines: {node: '>= 10.0.0'}

  unpipe@1.0.0:
    resolution: {integrity: sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==}
    engines: {node: '>= 0.8'}

  update-browserslist-db@1.1.3:
    resolution: {integrity: sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  use-sync-external-store@1.5.0:
    resolution: {integrity: sha512-Rb46I4cGGVBmjamjphe8L/UnvJD+uPPtTkNvX5mZgqdbavhI4EbgIWJiIHXJ8bc/i9EQGPRh4DwEURJ552Do0A==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  utils-merge@1.0.1:
    resolution: {integrity: sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==}
    engines: {node: '>= 0.4.0'}

  valibot@0.41.0:
    resolution: {integrity: sha512-igDBb8CTYr8YTQlOKgaN9nSS0Be7z+WRuaeYqGf3Cjz3aKmSnqEmYnkfVjzIuumGqfHpa3fLIvMEAfhrpqN8ng==}
    peerDependencies:
      typescript: '>=5'
    peerDependenciesMeta:
      typescript:
        optional: true

  validate-npm-package-license@3.0.4:
    resolution: {integrity: sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==}

  validate-npm-package-name@5.0.1:
    resolution: {integrity: sha512-OljLrQ9SQdOUqTaQxqL5dEfZWrXExyyWsozYlAWFawPVNuD83igl7uJD2RTkNMbniIYgt8l81eCJGIdQF7avLQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  vary@1.1.2:
    resolution: {integrity: sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==}
    engines: {node: '>= 0.8'}

  vite-node@3.2.3:
    resolution: {integrity: sha512-gc8aAifGuDIpZHrPjuHyP4dpQmYXqWw7D1GmDnWeNWP654UEXzVfQ5IHPSK5HaHkwB/+p1atpYpSdw/2kOv8iQ==}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22.0.0}
    hasBin: true

  vite-tsconfig-paths@5.1.4:
    resolution: {integrity: sha512-cYj0LRuLV2c2sMqhqhGpaO3LretdtMn/BVX4cPLanIZuwwrkVl+lK84E/miEXkCHWXuq65rhNN4rXsBcOB3S4w==}
    peerDependencies:
      vite: '*'
    peerDependenciesMeta:
      vite:
        optional: true

  vite@6.3.5:
    resolution: {integrity: sha512-cZn6NDFE7wdTpINgs++ZJ4N49W2vRp8LCKrn3Ob1kYNtOo21vfDoaV5GzBfLU4MovSAB8uNRm4jgzVQZ+mBzPQ==}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^18.0.0 || ^20.0.0 || >=22.0.0
      jiti: '>=1.21.0'
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      sass-embedded: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.16.0
      tsx: ^4.8.1
      yaml: ^2.4.2
    peerDependenciesMeta:
      '@types/node':
        optional: true
      jiti:
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
      tsx:
        optional: true
      yaml:
        optional: true

  which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true

  which@3.0.1:
    resolution: {integrity: sha512-XA1b62dzQzLfaEOSQFTCOd5KFf/1VSzZo7/7TUjnya6u0vGGKzU96UQBZTAThCb2j4/xjBAyii1OhRLJEivHvg==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}
    hasBin: true

  wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}

  wrap-ansi@8.1.0:
    resolution: {integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==}
    engines: {node: '>=12'}

  yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}

  yallist@5.0.0:
    resolution: {integrity: sha512-YgvUTfwqyc7UXVMrB+SImsVYSmTS8X/tSrtdNZMImM+n7+QTriRXyXim0mBrTXNeqzVF0KWGgHPeiyViFFrNDw==}
    engines: {node: '>=18'}

snapshots:

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25

  '@babel/code-frame@7.27.1':
    dependencies:
      '@babel/helper-validator-identifier': 7.27.1
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/compat-data@7.27.5': {}

  '@babel/core@7.27.4':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.27.5
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.27.4)
      '@babel/helpers': 7.27.6
      '@babel/parser': 7.27.5
      '@babel/template': 7.27.2
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
      convert-source-map: 2.0.0
      debug: 4.4.1
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.27.5':
    dependencies:
      '@babel/parser': 7.27.5
      '@babel/types': 7.27.6
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 3.0.2

  '@babel/helper-annotate-as-pure@7.27.3':
    dependencies:
      '@babel/types': 7.27.6

  '@babel/helper-compilation-targets@7.27.2':
    dependencies:
      '@babel/compat-data': 7.27.5
      '@babel/helper-validator-option': 7.27.1
      browserslist: 4.25.0
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-member-expression-to-functions': 7.27.1
      '@babel/helper-optimise-call-expression': 7.27.1
      '@babel/helper-replace-supers': 7.27.1(@babel/core@7.27.4)
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
      '@babel/traverse': 7.27.4
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-member-expression-to-functions@7.27.1':
    dependencies:
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-imports@7.27.1':
    dependencies:
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.27.3(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      '@babel/traverse': 7.27.4
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-optimise-call-expression@7.27.1':
    dependencies:
      '@babel/types': 7.27.6

  '@babel/helper-plugin-utils@7.27.1': {}

  '@babel/helper-replace-supers@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-member-expression-to-functions': 7.27.1
      '@babel/helper-optimise-call-expression': 7.27.1
      '@babel/traverse': 7.27.4
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    dependencies:
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-string-parser@7.27.1': {}

  '@babel/helper-validator-identifier@7.27.1': {}

  '@babel/helper-validator-option@7.27.1': {}

  '@babel/helpers@7.27.6':
    dependencies:
      '@babel/template': 7.27.2
      '@babel/types': 7.27.6

  '@babel/parser@7.27.5':
    dependencies:
      '@babel/types': 7.27.6

  '@babel/plugin-syntax-decorators@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-modules-commonjs@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.27.4)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-typescript@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-create-class-features-plugin': 7.27.1(@babel/core@7.27.4)
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
      '@babel/plugin-syntax-typescript': 7.27.1(@babel/core@7.27.4)
    transitivePeerDependencies:
      - supports-color

  '@babel/preset-typescript@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-validator-option': 7.27.1
      '@babel/plugin-syntax-jsx': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-modules-commonjs': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-typescript': 7.27.1(@babel/core@7.27.4)
    transitivePeerDependencies:
      - supports-color

  '@babel/runtime@7.27.6': {}

  '@babel/template@7.27.2':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/parser': 7.27.5
      '@babel/types': 7.27.6

  '@babel/traverse@7.27.4':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.27.5
      '@babel/parser': 7.27.5
      '@babel/template': 7.27.2
      '@babel/types': 7.27.6
      debug: 4.4.1
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.27.6':
    dependencies:
      '@babel/helper-string-parser': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1

  '@ctrl/tinycolor@3.6.1': {}

  '@emotion/hash@0.9.2': {}

  '@esbuild/aix-ppc64@0.25.5':
    optional: true

  '@esbuild/android-arm64@0.25.5':
    optional: true

  '@esbuild/android-arm@0.25.5':
    optional: true

  '@esbuild/android-x64@0.25.5':
    optional: true

  '@esbuild/darwin-arm64@0.25.5':
    optional: true

  '@esbuild/darwin-x64@0.25.5':
    optional: true

  '@esbuild/freebsd-arm64@0.25.5':
    optional: true

  '@esbuild/freebsd-x64@0.25.5':
    optional: true

  '@esbuild/linux-arm64@0.25.5':
    optional: true

  '@esbuild/linux-arm@0.25.5':
    optional: true

  '@esbuild/linux-ia32@0.25.5':
    optional: true

  '@esbuild/linux-loong64@0.25.5':
    optional: true

  '@esbuild/linux-mips64el@0.25.5':
    optional: true

  '@esbuild/linux-ppc64@0.25.5':
    optional: true

  '@esbuild/linux-riscv64@0.25.5':
    optional: true

  '@esbuild/linux-s390x@0.25.5':
    optional: true

  '@esbuild/linux-x64@0.25.5':
    optional: true

  '@esbuild/netbsd-arm64@0.25.5':
    optional: true

  '@esbuild/netbsd-x64@0.25.5':
    optional: true

  '@esbuild/openbsd-arm64@0.25.5':
    optional: true

  '@esbuild/openbsd-x64@0.25.5':
    optional: true

  '@esbuild/sunos-x64@0.25.5':
    optional: true

  '@esbuild/win32-arm64@0.25.5':
    optional: true

  '@esbuild/win32-ia32@0.25.5':
    optional: true

  '@esbuild/win32-x64@0.25.5':
    optional: true

  '@floating-ui/core@1.7.1':
    dependencies:
      '@floating-ui/utils': 0.2.9

  '@floating-ui/devtools@0.2.1(@floating-ui/dom@1.7.1)':
    dependencies:
      '@floating-ui/dom': 1.7.1

  '@floating-ui/dom@1.7.1':
    dependencies:
      '@floating-ui/core': 1.7.1
      '@floating-ui/utils': 0.2.9

  '@floating-ui/utils@0.2.9': {}

  '@fluentui/keyboard-keys@9.0.8':
    dependencies:
      '@swc/helpers': 0.5.17

  '@fluentui/priority-overflow@9.1.15':
    dependencies:
      '@swc/helpers': 0.5.17

  '@fluentui/react-accordion@9.7.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-aria': 9.15.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-motion': 9.8.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-motion-components-preview': 0.6.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-alert@9.0.0-beta.124(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-avatar': 9.8.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-button': 9.5.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-aria@9.15.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-avatar@9.8.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-badge': 9.3.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-popover': 9.11.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-tooltip': 9.7.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-badge@9.3.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-breadcrumb@9.2.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-aria': 9.15.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-button': 9.5.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-link': 9.5.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-button@9.5.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-card@9.3.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-text': 9.5.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-carousel@9.7.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-aria': 9.15.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-button': 9.5.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-tooltip': 9.7.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      embla-carousel: 8.6.0
      embla-carousel-autoplay: 8.6.0(embla-carousel@8.6.0)
      embla-carousel-fade: 8.6.0(embla-carousel@8.6.0)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-checkbox@9.4.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-field': 9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-label': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-color-picker@9.1.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@ctrl/tinycolor': 3.6.1
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-combobox@9.15.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-field': 9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-portal': 9.6.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-positioning': 9.18.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-components@9.66.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-accordion': 9.7.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-alert': 9.0.0-beta.124(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-aria': 9.15.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-avatar': 9.8.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-badge': 9.3.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-breadcrumb': 9.2.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-button': 9.5.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-card': 9.3.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-carousel': 9.7.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-checkbox': 9.4.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-color-picker': 9.1.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-combobox': 9.15.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-dialog': 9.13.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-divider': 9.3.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-drawer': 9.8.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-field': 9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-image': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-infobutton': 9.0.0-beta.102(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-infolabel': 9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-input': 9.6.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-label': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-link': 9.5.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-list': 9.2.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-menu': 9.17.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-message-bar': 9.5.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-motion': 9.8.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-nav': 9.1.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-overflow': 9.4.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-persona': 9.4.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-popover': 9.11.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-portal': 9.6.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-positioning': 9.18.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-progress': 9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-provider': 9.21.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-radio': 9.4.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-rating': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-search': 9.2.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-select': 9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-skeleton': 9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-slider': 9.4.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-spinbutton': 9.4.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-spinner': 9.6.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-swatch-picker': 9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-switch': 9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-table': 9.17.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-tabs': 9.8.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-tag-picker': 9.6.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-tags': 9.6.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-teaching-popover': 9.5.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-text': 9.5.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-textarea': 9.5.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-toast': 9.5.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-toolbar': 9.5.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-tooltip': 9.7.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-tree': 9.11.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-virtualizer': 9.0.0-alpha.98(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-context-selector@9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      scheduler: 0.23.2

  '@fluentui/react-dialog@9.13.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-motion': 9.8.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-motion-components-preview': 0.6.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-portal': 9.6.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-divider@9.3.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-drawer@9.8.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-dialog': 9.13.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-motion': 9.8.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-portal': 9.6.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-field@9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-label': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-icons@2.0.303(react@18.3.1)':
    dependencies:
      '@griffel/react': 1.5.30(react@18.3.1)
      react: 18.3.1
      tslib: 2.8.1

  '@fluentui/react-image@9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-infobutton@9.0.0-beta.102(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-label': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-popover': 9.11.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-infolabel@9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-label': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-popover': 9.11.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-input@9.6.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-field': 9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-jsx-runtime@9.1.0(@types/react@18.3.23)(react@18.3.1)':
    dependencies:
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      react: 18.3.1
      react-is: 17.0.2

  '@fluentui/react-label@9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-link@9.5.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-list@9.2.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-checkbox': 9.4.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-menu@9.17.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-portal': 9.6.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-positioning': 9.18.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-message-bar@9.5.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-button': 9.5.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-link': 9.5.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-transition-group: 4.4.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)

  '@fluentui/react-motion-components-preview@0.6.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-motion': 9.8.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-motion@9.8.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-nav@9.1.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-aria': 9.15.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-button': 9.5.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-divider': 9.3.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-drawer': 9.8.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-tooltip': 9.7.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-overflow@9.4.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/priority-overflow': 9.1.15
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-persona@9.4.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-avatar': 9.8.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-badge': 9.3.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-popover@9.11.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-portal': 9.6.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-positioning': 9.18.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-portal@9.6.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-positioning@9.18.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@floating-ui/devtools': 0.2.1(@floating-ui/dom@1.7.1)
      '@floating-ui/dom': 1.7.1
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      use-sync-external-store: 1.5.0(react@18.3.1)

  '@fluentui/react-progress@9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-field': 9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-provider@9.21.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/core': 1.19.2
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-radio@9.4.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-field': 9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-label': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-rating@9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-search@9.2.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-input': 9.6.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-select@9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-field': 9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.23)(react@18.3.1)':
    dependencies:
      '@fluentui/react-theme': 9.1.24
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      react: 18.3.1

  '@fluentui/react-skeleton@9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-field': 9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-slider@9.4.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-field': 9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-spinbutton@9.4.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-field': 9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-spinner@9.6.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-label': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-swatch-picker@9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-field': 9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-switch@9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-field': 9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-label': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-table@9.17.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-avatar': 9.8.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-checkbox': 9.4.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-radio': 9.4.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-tabs@9.8.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-tabster@9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      keyborg: 2.6.0
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      tabster: 8.5.6

  '@fluentui/react-tag-picker@9.6.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-combobox': 9.15.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-field': 9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-portal': 9.6.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-positioning': 9.18.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-tags': 9.6.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-tags@9.6.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-avatar': 9.8.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-teaching-popover@9.5.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-aria': 9.15.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-button': 9.5.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-popover': 9.11.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      use-sync-external-store: 1.5.0(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-text@9.5.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-textarea@9.5.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-field': 9.3.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-theme@9.1.24':
    dependencies:
      '@fluentui/tokens': 1.0.0-alpha.21
      '@swc/helpers': 0.5.17

  '@fluentui/react-toast@9.5.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-motion': 9.8.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-motion-components-preview': 0.6.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-portal': 9.6.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-toolbar@9.5.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-button': 9.5.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-divider': 9.3.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-radio': 9.4.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-tooltip@9.7.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-portal': 9.6.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-positioning': 9.18.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-tree@9.11.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-avatar': 9.8.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-button': 9.5.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-checkbox': 9.4.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-motion': 9.8.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-motion-components-preview': 0.6.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-radio': 9.4.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-utilities@9.21.0(@types/react@18.3.23)(react@18.3.1)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      react: 18.3.1

  '@fluentui/react-virtualizer@9.0.0-alpha.98(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.23)(react@18.3.1)
      '@fluentui/react-utilities': 9.21.0(@types/react@18.3.23)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/tokens@1.0.0-alpha.21':
    dependencies:
      '@swc/helpers': 0.5.17

  '@griffel/core@1.19.2':
    dependencies:
      '@emotion/hash': 0.9.2
      '@griffel/style-types': 1.3.0
      csstype: 3.1.3
      rtl-css-js: 1.16.1
      stylis: 4.3.6
      tslib: 2.8.1

  '@griffel/react@1.5.30(react@18.3.1)':
    dependencies:
      '@griffel/core': 1.19.2
      react: 18.3.1
      tslib: 2.8.1

  '@griffel/style-types@1.3.0':
    dependencies:
      csstype: 3.1.3

  '@isaacs/cliui@8.0.2':
    dependencies:
      string-width: 5.1.2
      string-width-cjs: string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: wrap-ansi@7.0.0

  '@isaacs/fs-minipass@4.0.1':
    dependencies:
      minipass: 7.1.2

  '@jridgewell/gen-mapping@0.3.8':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/sourcemap-codec@1.5.0': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  '@mjackson/node-fetch-server@0.2.0': {}

  '@npmcli/git@4.1.0':
    dependencies:
      '@npmcli/promise-spawn': 6.0.2
      lru-cache: 7.18.3
      npm-pick-manifest: 8.0.2
      proc-log: 3.0.0
      promise-inflight: 1.0.1
      promise-retry: 2.0.1
      semver: 7.7.2
      which: 3.0.1
    transitivePeerDependencies:
      - bluebird

  '@npmcli/package-json@4.0.1':
    dependencies:
      '@npmcli/git': 4.1.0
      glob: 10.4.5
      hosted-git-info: 6.1.3
      json-parse-even-better-errors: 3.0.2
      normalize-package-data: 5.0.0
      proc-log: 3.0.0
      semver: 7.7.2
    transitivePeerDependencies:
      - bluebird

  '@npmcli/promise-spawn@6.0.2':
    dependencies:
      which: 3.0.1

  '@pkgjs/parseargs@0.11.0':
    optional: true

  '@react-router/dev@7.6.2(@react-router/serve@7.6.2(react-router@7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(typescript@5.8.3))(@types/node@20.19.0)(jiti@2.4.2)(lightningcss@1.30.1)(react-router@7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(typescript@5.8.3)(vite@6.3.5(@types/node@20.19.0)(jiti@2.4.2)(lightningcss@1.30.1))':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/generator': 7.27.5
      '@babel/parser': 7.27.5
      '@babel/plugin-syntax-decorators': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-syntax-jsx': 7.27.1(@babel/core@7.27.4)
      '@babel/preset-typescript': 7.27.1(@babel/core@7.27.4)
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
      '@npmcli/package-json': 4.0.1
      '@react-router/node': 7.6.2(react-router@7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(typescript@5.8.3)
      arg: 5.0.2
      babel-dead-code-elimination: 1.0.10
      chokidar: 4.0.3
      dedent: 1.6.0
      es-module-lexer: 1.7.0
      exit-hook: 2.2.1
      fs-extra: 10.1.0
      jsesc: 3.0.2
      lodash: 4.17.21
      pathe: 1.1.2
      picocolors: 1.1.1
      prettier: 2.8.8
      react-refresh: 0.14.2
      react-router: 7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      semver: 7.7.2
      set-cookie-parser: 2.7.1
      valibot: 0.41.0(typescript@5.8.3)
      vite: 6.3.5(@types/node@20.19.0)(jiti@2.4.2)(lightningcss@1.30.1)
      vite-node: 3.2.3(@types/node@20.19.0)(jiti@2.4.2)(lightningcss@1.30.1)
    optionalDependencies:
      '@react-router/serve': 7.6.2(react-router@7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - '@types/node'
      - babel-plugin-macros
      - bluebird
      - jiti
      - less
      - lightningcss
      - sass
      - sass-embedded
      - stylus
      - sugarss
      - supports-color
      - terser
      - tsx
      - yaml

  '@react-router/express@7.6.2(express@4.21.2)(react-router@7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(typescript@5.8.3)':
    dependencies:
      '@react-router/node': 7.6.2(react-router@7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(typescript@5.8.3)
      express: 4.21.2
      react-router: 7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
    optionalDependencies:
      typescript: 5.8.3

  '@react-router/node@7.6.2(react-router@7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(typescript@5.8.3)':
    dependencies:
      '@mjackson/node-fetch-server': 0.2.0
      react-router: 7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      source-map-support: 0.5.21
      stream-slice: 0.1.2
      undici: 6.21.3
    optionalDependencies:
      typescript: 5.8.3

  '@react-router/serve@7.6.2(react-router@7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(typescript@5.8.3)':
    dependencies:
      '@react-router/express': 7.6.2(express@4.21.2)(react-router@7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(typescript@5.8.3)
      '@react-router/node': 7.6.2(react-router@7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(typescript@5.8.3)
      compression: 1.8.0
      express: 4.21.2
      get-port: 5.1.1
      morgan: 1.10.0
      react-router: 7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      source-map-support: 0.5.21
    transitivePeerDependencies:
      - supports-color
      - typescript

  '@rollup/rollup-android-arm-eabi@4.43.0':
    optional: true

  '@rollup/rollup-android-arm64@4.43.0':
    optional: true

  '@rollup/rollup-darwin-arm64@4.43.0':
    optional: true

  '@rollup/rollup-darwin-x64@4.43.0':
    optional: true

  '@rollup/rollup-freebsd-arm64@4.43.0':
    optional: true

  '@rollup/rollup-freebsd-x64@4.43.0':
    optional: true

  '@rollup/rollup-linux-arm-gnueabihf@4.43.0':
    optional: true

  '@rollup/rollup-linux-arm-musleabihf@4.43.0':
    optional: true

  '@rollup/rollup-linux-arm64-gnu@4.43.0':
    optional: true

  '@rollup/rollup-linux-arm64-musl@4.43.0':
    optional: true

  '@rollup/rollup-linux-loongarch64-gnu@4.43.0':
    optional: true

  '@rollup/rollup-linux-powerpc64le-gnu@4.43.0':
    optional: true

  '@rollup/rollup-linux-riscv64-gnu@4.43.0':
    optional: true

  '@rollup/rollup-linux-riscv64-musl@4.43.0':
    optional: true

  '@rollup/rollup-linux-s390x-gnu@4.43.0':
    optional: true

  '@rollup/rollup-linux-x64-gnu@4.40.0':
    optional: true

  '@rollup/rollup-linux-x64-gnu@4.43.0':
    optional: true

  '@rollup/rollup-linux-x64-musl@4.43.0':
    optional: true

  '@rollup/rollup-win32-arm64-msvc@4.43.0':
    optional: true

  '@rollup/rollup-win32-ia32-msvc@4.43.0':
    optional: true

  '@rollup/rollup-win32-x64-msvc@4.43.0':
    optional: true

  '@swc/helpers@0.5.17':
    dependencies:
      tslib: 2.8.1

  '@tailwindcss/node@4.1.10':
    dependencies:
      '@ampproject/remapping': 2.3.0
      enhanced-resolve: 5.18.1
      jiti: 2.4.2
      lightningcss: 1.30.1
      magic-string: 0.30.17
      source-map-js: 1.2.1
      tailwindcss: 4.1.10

  '@tailwindcss/oxide-android-arm64@4.1.10':
    optional: true

  '@tailwindcss/oxide-darwin-arm64@4.1.10':
    optional: true

  '@tailwindcss/oxide-darwin-x64@4.1.10':
    optional: true

  '@tailwindcss/oxide-freebsd-x64@4.1.10':
    optional: true

  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.10':
    optional: true

  '@tailwindcss/oxide-linux-arm64-gnu@4.1.10':
    optional: true

  '@tailwindcss/oxide-linux-arm64-musl@4.1.10':
    optional: true

  '@tailwindcss/oxide-linux-x64-gnu@4.1.10':
    optional: true

  '@tailwindcss/oxide-linux-x64-musl@4.1.10':
    optional: true

  '@tailwindcss/oxide-wasm32-wasi@4.1.10':
    optional: true

  '@tailwindcss/oxide-win32-arm64-msvc@4.1.10':
    optional: true

  '@tailwindcss/oxide-win32-x64-msvc@4.1.10':
    optional: true

  '@tailwindcss/oxide@4.1.10':
    dependencies:
      detect-libc: 2.0.4
      tar: 7.4.3
    optionalDependencies:
      '@tailwindcss/oxide-android-arm64': 4.1.10
      '@tailwindcss/oxide-darwin-arm64': 4.1.10
      '@tailwindcss/oxide-darwin-x64': 4.1.10
      '@tailwindcss/oxide-freebsd-x64': 4.1.10
      '@tailwindcss/oxide-linux-arm-gnueabihf': 4.1.10
      '@tailwindcss/oxide-linux-arm64-gnu': 4.1.10
      '@tailwindcss/oxide-linux-arm64-musl': 4.1.10
      '@tailwindcss/oxide-linux-x64-gnu': 4.1.10
      '@tailwindcss/oxide-linux-x64-musl': 4.1.10
      '@tailwindcss/oxide-wasm32-wasi': 4.1.10
      '@tailwindcss/oxide-win32-arm64-msvc': 4.1.10
      '@tailwindcss/oxide-win32-x64-msvc': 4.1.10

  '@tailwindcss/vite@4.1.10(vite@6.3.5(@types/node@20.19.0)(jiti@2.4.2)(lightningcss@1.30.1))':
    dependencies:
      '@tailwindcss/node': 4.1.10
      '@tailwindcss/oxide': 4.1.10
      tailwindcss: 4.1.10
      vite: 6.3.5(@types/node@20.19.0)(jiti@2.4.2)(lightningcss@1.30.1)

  '@types/estree@1.0.7': {}

  '@types/node@20.19.0':
    dependencies:
      undici-types: 6.21.0

  '@types/prop-types@15.7.15': {}

  '@types/react-dom@18.3.7(@types/react@18.3.23)':
    dependencies:
      '@types/react': 18.3.23

  '@types/react-dom@19.1.6(@types/react@19.1.8)':
    dependencies:
      '@types/react': 19.1.8

  '@types/react@18.3.23':
    dependencies:
      '@types/prop-types': 15.7.15
      csstype: 3.1.3

  '@types/react@19.1.8':
    dependencies:
      csstype: 3.1.3

  accepts@1.3.8:
    dependencies:
      mime-types: 2.1.35
      negotiator: 0.6.3

  ansi-regex@5.0.1: {}

  ansi-regex@6.1.0: {}

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@6.2.1: {}

  arg@5.0.2: {}

  array-flatten@1.1.1: {}

  babel-dead-code-elimination@1.0.10:
    dependencies:
      '@babel/core': 7.27.4
      '@babel/parser': 7.27.5
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
    transitivePeerDependencies:
      - supports-color

  balanced-match@1.0.2: {}

  basic-auth@2.0.1:
    dependencies:
      safe-buffer: 5.1.2

  body-parser@1.20.3:
    dependencies:
      bytes: 3.1.2
      content-type: 1.0.5
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      on-finished: 2.4.1
      qs: 6.13.0
      raw-body: 2.5.2
      type-is: 1.6.18
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  brace-expansion@2.0.2:
    dependencies:
      balanced-match: 1.0.2

  browserslist@4.25.0:
    dependencies:
      caniuse-lite: 1.0.30001723
      electron-to-chromium: 1.5.167
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3(browserslist@4.25.0)

  buffer-from@1.1.2: {}

  bytes@3.1.2: {}

  cac@6.7.14: {}

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  call-bound@1.0.4:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      get-intrinsic: 1.3.0

  caniuse-lite@1.0.30001723: {}

  chokidar@4.0.3:
    dependencies:
      readdirp: 4.1.2

  chownr@3.0.0: {}

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.4: {}

  compressible@2.0.18:
    dependencies:
      mime-db: 1.54.0

  compression@1.8.0:
    dependencies:
      bytes: 3.1.2
      compressible: 2.0.18
      debug: 2.6.9
      negotiator: 0.6.4
      on-headers: 1.0.2
      safe-buffer: 5.2.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  content-disposition@0.5.4:
    dependencies:
      safe-buffer: 5.2.1

  content-type@1.0.5: {}

  convert-source-map@2.0.0: {}

  cookie-signature@1.0.6: {}

  cookie@0.7.1: {}

  cookie@1.0.2: {}

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  csstype@3.1.3: {}

  debug@2.6.9:
    dependencies:
      ms: 2.0.0

  debug@4.4.1:
    dependencies:
      ms: 2.1.3

  dedent@1.6.0: {}

  depd@2.0.0: {}

  destroy@1.2.0: {}

  detect-libc@2.0.4: {}

  dom-helpers@5.2.1:
    dependencies:
      '@babel/runtime': 7.27.6
      csstype: 3.1.3

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  eastasianwidth@0.2.0: {}

  ee-first@1.1.1: {}

  electron-to-chromium@1.5.167: {}

  embla-carousel-autoplay@8.6.0(embla-carousel@8.6.0):
    dependencies:
      embla-carousel: 8.6.0

  embla-carousel-fade@8.6.0(embla-carousel@8.6.0):
    dependencies:
      embla-carousel: 8.6.0

  embla-carousel@8.6.0: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  encodeurl@1.0.2: {}

  encodeurl@2.0.0: {}

  enhanced-resolve@5.18.1:
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.2

  err-code@2.0.3: {}

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-module-lexer@1.7.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  esbuild@0.25.5:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.25.5
      '@esbuild/android-arm': 0.25.5
      '@esbuild/android-arm64': 0.25.5
      '@esbuild/android-x64': 0.25.5
      '@esbuild/darwin-arm64': 0.25.5
      '@esbuild/darwin-x64': 0.25.5
      '@esbuild/freebsd-arm64': 0.25.5
      '@esbuild/freebsd-x64': 0.25.5
      '@esbuild/linux-arm': 0.25.5
      '@esbuild/linux-arm64': 0.25.5
      '@esbuild/linux-ia32': 0.25.5
      '@esbuild/linux-loong64': 0.25.5
      '@esbuild/linux-mips64el': 0.25.5
      '@esbuild/linux-ppc64': 0.25.5
      '@esbuild/linux-riscv64': 0.25.5
      '@esbuild/linux-s390x': 0.25.5
      '@esbuild/linux-x64': 0.25.5
      '@esbuild/netbsd-arm64': 0.25.5
      '@esbuild/netbsd-x64': 0.25.5
      '@esbuild/openbsd-arm64': 0.25.5
      '@esbuild/openbsd-x64': 0.25.5
      '@esbuild/sunos-x64': 0.25.5
      '@esbuild/win32-arm64': 0.25.5
      '@esbuild/win32-ia32': 0.25.5
      '@esbuild/win32-x64': 0.25.5

  escalade@3.2.0: {}

  escape-html@1.0.3: {}

  etag@1.8.1: {}

  exit-hook@2.2.1: {}

  express@4.21.2:
    dependencies:
      accepts: 1.3.8
      array-flatten: 1.1.1
      body-parser: 1.20.3
      content-disposition: 0.5.4
      content-type: 1.0.5
      cookie: 0.7.1
      cookie-signature: 1.0.6
      debug: 2.6.9
      depd: 2.0.0
      encodeurl: 2.0.0
      escape-html: 1.0.3
      etag: 1.8.1
      finalhandler: 1.3.1
      fresh: 0.5.2
      http-errors: 2.0.0
      merge-descriptors: 1.0.3
      methods: 1.1.2
      on-finished: 2.4.1
      parseurl: 1.3.3
      path-to-regexp: 0.1.12
      proxy-addr: 2.0.7
      qs: 6.13.0
      range-parser: 1.2.1
      safe-buffer: 5.2.1
      send: 0.19.0
      serve-static: 1.16.2
      setprototypeof: 1.2.0
      statuses: 2.0.1
      type-is: 1.6.18
      utils-merge: 1.0.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  fdir@6.4.6(picomatch@4.0.2):
    optionalDependencies:
      picomatch: 4.0.2

  finalhandler@1.3.1:
    dependencies:
      debug: 2.6.9
      encodeurl: 2.0.0
      escape-html: 1.0.3
      on-finished: 2.4.1
      parseurl: 1.3.3
      statuses: 2.0.1
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  foreground-child@3.3.1:
    dependencies:
      cross-spawn: 7.0.6
      signal-exit: 4.1.0

  forwarded@0.2.0: {}

  fresh@0.5.2: {}

  fs-extra@10.1.0:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  gensync@1.0.0-beta.2: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-port@5.1.1: {}

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  glob@10.4.5:
    dependencies:
      foreground-child: 3.3.1
      jackspeak: 3.4.3
      minimatch: 9.0.5
      minipass: 7.1.2
      package-json-from-dist: 1.0.1
      path-scurry: 1.11.1

  globals@11.12.0: {}

  globrex@0.1.2: {}

  gopd@1.2.0: {}

  graceful-fs@4.2.11: {}

  has-symbols@1.1.0: {}

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  hosted-git-info@6.1.3:
    dependencies:
      lru-cache: 7.18.3

  http-errors@2.0.0:
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1

  iconv-lite@0.4.24:
    dependencies:
      safer-buffer: 2.1.2

  inherits@2.0.4: {}

  ipaddr.js@1.9.1: {}

  is-core-module@2.16.1:
    dependencies:
      hasown: 2.0.2

  is-fullwidth-code-point@3.0.0: {}

  isbot@5.1.28: {}

  isexe@2.0.0: {}

  jackspeak@3.4.3:
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0

  jiti@2.4.2: {}

  js-tokens@4.0.0: {}

  jsesc@3.0.2: {}

  json-parse-even-better-errors@3.0.2: {}

  json5@2.2.3: {}

  jsonfile@6.1.0:
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11

  keyborg@2.6.0: {}

  lightningcss-darwin-arm64@1.30.1:
    optional: true

  lightningcss-darwin-x64@1.30.1:
    optional: true

  lightningcss-freebsd-x64@1.30.1:
    optional: true

  lightningcss-linux-arm-gnueabihf@1.30.1:
    optional: true

  lightningcss-linux-arm64-gnu@1.30.1:
    optional: true

  lightningcss-linux-arm64-musl@1.30.1:
    optional: true

  lightningcss-linux-x64-gnu@1.30.1:
    optional: true

  lightningcss-linux-x64-musl@1.30.1:
    optional: true

  lightningcss-win32-arm64-msvc@1.30.1:
    optional: true

  lightningcss-win32-x64-msvc@1.30.1:
    optional: true

  lightningcss@1.30.1:
    dependencies:
      detect-libc: 2.0.4
    optionalDependencies:
      lightningcss-darwin-arm64: 1.30.1
      lightningcss-darwin-x64: 1.30.1
      lightningcss-freebsd-x64: 1.30.1
      lightningcss-linux-arm-gnueabihf: 1.30.1
      lightningcss-linux-arm64-gnu: 1.30.1
      lightningcss-linux-arm64-musl: 1.30.1
      lightningcss-linux-x64-gnu: 1.30.1
      lightningcss-linux-x64-musl: 1.30.1
      lightningcss-win32-arm64-msvc: 1.30.1
      lightningcss-win32-x64-msvc: 1.30.1

  lodash@4.17.21: {}

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  lru-cache@10.4.3: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  lru-cache@7.18.3: {}

  magic-string@0.30.17:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.0

  math-intrinsics@1.1.0: {}

  media-typer@0.3.0: {}

  merge-descriptors@1.0.3: {}

  methods@1.1.2: {}

  mime-db@1.52.0: {}

  mime-db@1.54.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime@1.6.0: {}

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.2

  minipass@7.1.2: {}

  minizlib@3.0.2:
    dependencies:
      minipass: 7.1.2

  mkdirp@3.0.1: {}

  morgan@1.10.0:
    dependencies:
      basic-auth: 2.0.1
      debug: 2.6.9
      depd: 2.0.0
      on-finished: 2.3.0
      on-headers: 1.0.2
    transitivePeerDependencies:
      - supports-color

  ms@2.0.0: {}

  ms@2.1.3: {}

  nanoid@3.3.11: {}

  negotiator@0.6.3: {}

  negotiator@0.6.4: {}

  node-releases@2.0.19: {}

  normalize-package-data@5.0.0:
    dependencies:
      hosted-git-info: 6.1.3
      is-core-module: 2.16.1
      semver: 7.7.2
      validate-npm-package-license: 3.0.4

  npm-install-checks@6.3.0:
    dependencies:
      semver: 7.7.2

  npm-normalize-package-bin@3.0.1: {}

  npm-package-arg@10.1.0:
    dependencies:
      hosted-git-info: 6.1.3
      proc-log: 3.0.0
      semver: 7.7.2
      validate-npm-package-name: 5.0.1

  npm-pick-manifest@8.0.2:
    dependencies:
      npm-install-checks: 6.3.0
      npm-normalize-package-bin: 3.0.1
      npm-package-arg: 10.1.0
      semver: 7.7.2

  object-assign@4.1.1: {}

  object-inspect@1.13.4: {}

  on-finished@2.3.0:
    dependencies:
      ee-first: 1.1.1

  on-finished@2.4.1:
    dependencies:
      ee-first: 1.1.1

  on-headers@1.0.2: {}

  package-json-from-dist@1.0.1: {}

  parseurl@1.3.3: {}

  path-key@3.1.1: {}

  path-scurry@1.11.1:
    dependencies:
      lru-cache: 10.4.3
      minipass: 7.1.2

  path-to-regexp@0.1.12: {}

  pathe@1.1.2: {}

  pathe@2.0.3: {}

  picocolors@1.1.1: {}

  picomatch@4.0.2: {}

  postcss@8.5.5:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  prettier@2.8.8: {}

  proc-log@3.0.0: {}

  promise-inflight@1.0.1: {}

  promise-retry@2.0.1:
    dependencies:
      err-code: 2.0.3
      retry: 0.12.0

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  proxy-addr@2.0.7:
    dependencies:
      forwarded: 0.2.0
      ipaddr.js: 1.9.1

  qs@6.13.0:
    dependencies:
      side-channel: 1.1.0

  range-parser@1.2.1: {}

  raw-body@2.5.2:
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      unpipe: 1.0.0

  react-dom@18.3.1(react@18.3.1):
    dependencies:
      loose-envify: 1.4.0
      react: 18.3.1
      scheduler: 0.23.2

  react-is@16.13.1: {}

  react-is@17.0.2: {}

  react-refresh@0.14.2: {}

  react-router@7.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      cookie: 1.0.2
      react: 18.3.1
      set-cookie-parser: 2.7.1
    optionalDependencies:
      react-dom: 18.3.1(react@18.3.1)

  react-transition-group@4.4.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.27.6
      dom-helpers: 5.2.1
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  react@18.3.1:
    dependencies:
      loose-envify: 1.4.0

  readdirp@4.1.2: {}

  retry@0.12.0: {}

  rollup@4.43.0:
    dependencies:
      '@types/estree': 1.0.7
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.43.0
      '@rollup/rollup-android-arm64': 4.43.0
      '@rollup/rollup-darwin-arm64': 4.43.0
      '@rollup/rollup-darwin-x64': 4.43.0
      '@rollup/rollup-freebsd-arm64': 4.43.0
      '@rollup/rollup-freebsd-x64': 4.43.0
      '@rollup/rollup-linux-arm-gnueabihf': 4.43.0
      '@rollup/rollup-linux-arm-musleabihf': 4.43.0
      '@rollup/rollup-linux-arm64-gnu': 4.43.0
      '@rollup/rollup-linux-arm64-musl': 4.43.0
      '@rollup/rollup-linux-loongarch64-gnu': 4.43.0
      '@rollup/rollup-linux-powerpc64le-gnu': 4.43.0
      '@rollup/rollup-linux-riscv64-gnu': 4.43.0
      '@rollup/rollup-linux-riscv64-musl': 4.43.0
      '@rollup/rollup-linux-s390x-gnu': 4.43.0
      '@rollup/rollup-linux-x64-gnu': 4.43.0
      '@rollup/rollup-linux-x64-musl': 4.43.0
      '@rollup/rollup-win32-arm64-msvc': 4.43.0
      '@rollup/rollup-win32-ia32-msvc': 4.43.0
      '@rollup/rollup-win32-x64-msvc': 4.43.0
      fsevents: 2.3.3

  rtl-css-js@1.16.1:
    dependencies:
      '@babel/runtime': 7.27.6

  safe-buffer@5.1.2: {}

  safe-buffer@5.2.1: {}

  safer-buffer@2.1.2: {}

  scheduler@0.23.2:
    dependencies:
      loose-envify: 1.4.0

  semver@6.3.1: {}

  semver@7.7.2: {}

  send@0.19.0:
    dependencies:
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 2.0.0
      mime: 1.6.0
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.1
    transitivePeerDependencies:
      - supports-color

  serve-static@1.16.2:
    dependencies:
      encodeurl: 2.0.0
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 0.19.0
    transitivePeerDependencies:
      - supports-color

  set-cookie-parser@2.7.1: {}

  setprototypeof@1.2.0: {}

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  side-channel-list@1.0.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4

  side-channel-map@1.0.1:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4

  side-channel-weakmap@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4
      side-channel-map: 1.0.1

  side-channel@1.1.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4
      side-channel-list: 1.0.0
      side-channel-map: 1.0.1
      side-channel-weakmap: 1.0.2

  signal-exit@4.1.0: {}

  source-map-js@1.2.1: {}

  source-map-support@0.5.21:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map@0.6.1: {}

  spdx-correct@3.2.0:
    dependencies:
      spdx-expression-parse: 3.0.1
      spdx-license-ids: 3.0.21

  spdx-exceptions@2.5.0: {}

  spdx-expression-parse@3.0.1:
    dependencies:
      spdx-exceptions: 2.5.0
      spdx-license-ids: 3.0.21

  spdx-license-ids@3.0.21: {}

  statuses@2.0.1: {}

  stream-slice@0.1.2: {}

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.1.0

  stylis@4.3.6: {}

  tabster@8.5.6:
    dependencies:
      keyborg: 2.6.0
      tslib: 2.8.1
    optionalDependencies:
      '@rollup/rollup-linux-x64-gnu': 4.40.0

  tailwindcss@4.1.10: {}

  tapable@2.2.2: {}

  tar@7.4.3:
    dependencies:
      '@isaacs/fs-minipass': 4.0.1
      chownr: 3.0.0
      minipass: 7.1.2
      minizlib: 3.0.2
      mkdirp: 3.0.1
      yallist: 5.0.0

  tinyglobby@0.2.14:
    dependencies:
      fdir: 6.4.6(picomatch@4.0.2)
      picomatch: 4.0.2

  toidentifier@1.0.1: {}

  tsconfck@3.1.6(typescript@5.8.3):
    optionalDependencies:
      typescript: 5.8.3

  tslib@2.8.1: {}

  turbo-darwin-64@2.5.4:
    optional: true

  turbo-darwin-arm64@2.5.4:
    optional: true

  turbo-linux-64@2.5.4:
    optional: true

  turbo-linux-arm64@2.5.4:
    optional: true

  turbo-windows-64@2.5.4:
    optional: true

  turbo-windows-arm64@2.5.4:
    optional: true

  turbo@2.5.4:
    optionalDependencies:
      turbo-darwin-64: 2.5.4
      turbo-darwin-arm64: 2.5.4
      turbo-linux-64: 2.5.4
      turbo-linux-arm64: 2.5.4
      turbo-windows-64: 2.5.4
      turbo-windows-arm64: 2.5.4

  type-is@1.6.18:
    dependencies:
      media-typer: 0.3.0
      mime-types: 2.1.35

  typescript@5.8.3: {}

  undici-types@6.21.0: {}

  undici@6.21.3: {}

  universalify@2.0.1: {}

  unpipe@1.0.0: {}

  update-browserslist-db@1.1.3(browserslist@4.25.0):
    dependencies:
      browserslist: 4.25.0
      escalade: 3.2.0
      picocolors: 1.1.1

  use-sync-external-store@1.5.0(react@18.3.1):
    dependencies:
      react: 18.3.1

  utils-merge@1.0.1: {}

  valibot@0.41.0(typescript@5.8.3):
    optionalDependencies:
      typescript: 5.8.3

  validate-npm-package-license@3.0.4:
    dependencies:
      spdx-correct: 3.2.0
      spdx-expression-parse: 3.0.1

  validate-npm-package-name@5.0.1: {}

  vary@1.1.2: {}

  vite-node@3.2.3(@types/node@20.19.0)(jiti@2.4.2)(lightningcss@1.30.1):
    dependencies:
      cac: 6.7.14
      debug: 4.4.1
      es-module-lexer: 1.7.0
      pathe: 2.0.3
      vite: 6.3.5(@types/node@20.19.0)(jiti@2.4.2)(lightningcss@1.30.1)
    transitivePeerDependencies:
      - '@types/node'
      - jiti
      - less
      - lightningcss
      - sass
      - sass-embedded
      - stylus
      - sugarss
      - supports-color
      - terser
      - tsx
      - yaml

  vite-tsconfig-paths@5.1.4(typescript@5.8.3)(vite@6.3.5(@types/node@20.19.0)(jiti@2.4.2)(lightningcss@1.30.1)):
    dependencies:
      debug: 4.4.1
      globrex: 0.1.2
      tsconfck: 3.1.6(typescript@5.8.3)
    optionalDependencies:
      vite: 6.3.5(@types/node@20.19.0)(jiti@2.4.2)(lightningcss@1.30.1)
    transitivePeerDependencies:
      - supports-color
      - typescript

  vite@6.3.5(@types/node@20.19.0)(jiti@2.4.2)(lightningcss@1.30.1):
    dependencies:
      esbuild: 0.25.5
      fdir: 6.4.6(picomatch@4.0.2)
      picomatch: 4.0.2
      postcss: 8.5.5
      rollup: 4.43.0
      tinyglobby: 0.2.14
    optionalDependencies:
      '@types/node': 20.19.0
      fsevents: 2.3.3
      jiti: 2.4.2
      lightningcss: 1.30.1

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  which@3.0.1:
    dependencies:
      isexe: 2.0.0

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@8.1.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0

  yallist@3.1.1: {}

  yallist@5.0.0: {}
